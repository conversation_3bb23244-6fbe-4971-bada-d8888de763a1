 import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:google_fonts/google_fonts.dart';
import '../data/surah_names.dart';
import 'surah_player_screen.dart';
import 'favorites_screen.dart';
import 'package:provider/provider.dart';
import '../providers/theme_provider.dart';
import '../providers/background_audio_provider.dart'; // استخدام مزود الصوت المحسن

class QuranPlayerScreen extends StatefulWidget {
  const QuranPlayerScreen({super.key});

  @override
  State<QuranPlayerScreen> createState() => _QuranPlayerScreenState();
}

class _QuranPlayerScreenState extends State<QuranPlayerScreen> {
  List<String> audioFiles = [];
  bool isDarkMode = false;
  bool isArabic = true;

  @override
  void initState() {
    super.initState();
    loadAudioFiles();
  }

  void loadAudioFiles() {
    try {
      // استخدام مزود الصوت المحسن للحصول على قائمة الملفات الصوتية
      final audioProvider = Provider.of<BackgroundAudioProvider>(context, listen: false);
      setState(() {
        audioFiles = audioProvider.audioFiles;
      });
    } catch (e) {
      debugPrint('Error loading audio files: $e');
      // استخدام قائمة ثابتة في حالة فشل الحصول على القائمة من المزود
      setState(() {
        audioFiles = [
          'Abasa.m4a',
          'AL adeat.m4a',
          'AL ahkaf.m4a',
          'AL ankabot-Alqare Abdulrahman Mosed.m4a',
          'AL asr.m4a',
          'AL baenah.m4a',
          'AL balad.m4a',
          'AL borog.m4a',
          'AL dareat.m4a',
          'AL doha.m4a',
          'AL eklas.m4a',
          'AL enfetar.m4a',
          'AL ensan.m4a',
          'AL enshekak.m4a',
          'AL fager.m4a',
          'AL falak.m4a',
          'AL fatehah.m4a',
          'AL fath.m4a',
          'AL feel.m4a',
          'AL forkan.m4a',
          'AL gasheah.m4a',
          'AL gateah.m4a',
          'AL gomah.m4a',
          'AL haag.m4a',
          'AL haded.m4a',
          'AL hakah.m4a',
          'AL hashr.m4a',
          'AL hgrat.m4a',
          'AL homazah.m4a',
          'AL kadr.m4a',
          'AL kamar.m4a',
          'AL kaotar.m4a',
          'AL kareah.m4a',
          'AL keamah.m4a',
          'AL lael.m4a',
          'AL maareg.m4a',
          'AL maedah.m4a',
          'AL maon.m4a',
          'AL modater.m4a',
          'AL mogadalh.m4a',
          'AL momenon.m4a',
          'AL momtahnah.m4a',
          'AL monafkon.m4a',
          'AL morsalat.m4a',
          'AL motafefen.m4a',
          'AL mozamel.m4a',
          'AL nabaa.m4a',
          'AL nagm.m4a',
          'AL naml.m4a',
          'AL nas.m4a',
          'AL nasr.m4a',
          'AL noor.m4a',
          'AL oakeah.m4a',
          'AL rahman.m4a',
          'AL saf.m4a',
          'AL safat.m4a',
          'AL shams.m4a',
          'AL sharh.m4a',
          'AL shoara.m4a',
          'AL tahrem.m4a',
          'AL takator.m4a',
          'AL takoer.m4a',
          'AL talak.m4a',
          'AL tarek.m4a',
          'AL teen.m4a',
          'AL tgabon.m4a',
          'AL toor.m4a',
          'AL zolzolah.m4a',
          'AL zomor.m4a',
          'ALamran.m4a',
          'Doaa AL htm.m4a',
          'Ekraa.m4a',
          'Mohamad.m4a',
          'Nooh.m4a',
          'Qaf.m4a',
          'Qoraesh.m4a',
          'Tabat.m4a',
        ];
      });
    }
  }

  ThemeData lightTheme = ThemeData(
    brightness: Brightness.light,
    primaryColor: const Color(0xFF4C8C7D),
  );

  ThemeData darkTheme = ThemeData(
    brightness: Brightness.dark,
    primaryColor: Colors.black,
  );

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);

    return Container( // Wrap Scaffold with Container for background image
      decoration: const BoxDecoration(
        image: DecorationImage(
          image: AssetImage('assets/images/kalfea1.jpg'), // تغيير الخلفية إلى kalfea1.jpg
          fit: BoxFit.cover, // تغطية الشاشة بالكامل
        ),
      ),
      child: Scaffold(
        backgroundColor: Colors.transparent, // Make Scaffold background transparent
        extendBodyBehindAppBar: true, // تمديد المحتوى خلف الشريط العلوي
        appBar: AppBar(
          backgroundColor: Colors.transparent, // جعل خلفية الشريط العلوي شفافة
          leading: IconButton(
            icon: const Icon(Icons.arrow_back, color: Colors.white), // جعل زر الرجوع باللون الأبيض
            onPressed: () => Navigator.pop(context),
          ),
          title: const Text(
            'الشيخ خالد الجليل',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold, color: Colors.white),
          ),
          centerTitle: true,
          elevation: 0,
          actions: [
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert, color: Colors.white),
            onSelected: (value) {
              switch (value) {
                case 'dark_mode':
                  themeProvider.toggleTheme();
                  break;
                  case 'about_app':
                    showDialog(
                      context: context,
                      builder: (context) => AlertDialog(
                        contentPadding: const EdgeInsets.fromLTRB(24, 20, 24, 24),
                        insetPadding: const EdgeInsets.symmetric(horizontal: 40, vertical: 24),
                        title: Text(
                          'عن التطبيق',
                          textAlign: TextAlign.center,
                          style: GoogleFonts.cairo(
                            fontSize: 26,
                            fontWeight: FontWeight.bold,
                            color: const Color(0xFF4C8C7D),
                            letterSpacing: 0.5,
                          ),
                        ),
                        content: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              'تطبيق رفيق المسلم',
                              textAlign: TextAlign.center,
                              style: GoogleFonts.reemKufi(
                                fontSize: 22,
                                fontWeight: FontWeight.bold,
                                color: const Color(0xFF4C8C7D),
                                height: 1.3,
                              ),
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'تطبيق شامل يحتوي على:',
                              textAlign: TextAlign.center,
                              style: GoogleFonts.tajawal(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                height: 1.3,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              '• القرآن الكريم كاملاً\n'
                              '• الاستماع للسور بصوت الشيخ خالد الجليل\n'
                              '• أذكار وأدعية متنوعة\n'
                              '• المسبحة الإلكترونية\n'
                              '• قصص الأنبياء\n'
                              '• أسماء الله الحسنى',
                              textAlign: TextAlign.right,
                              style: GoogleFonts.cairo(
                                fontSize: 16,
                                height: 1.5,
                                color: Colors.black87,
                              ),
                            ),
                            const SizedBox(height: 12),
                            Text(
                              'الإصدار: 1.0.0',
                              textAlign: TextAlign.center,
                              style: GoogleFonts.tajawal(
                                fontSize: 14,
                                fontStyle: FontStyle.italic,
                                color: Colors.grey[700],
                              ),
                            ),
                          ],
                        ),
                        actions: [
                          Center(
                            child: ElevatedButton(
                              onPressed: () => Navigator.pop(context),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: const Color(0xFF4C8C7D),
                                foregroundColor: Colors.white,
                                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 10),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                              child: Text(
                                'حسناً',
                                style: GoogleFonts.tajawal(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
                    break;
                  case 'about_dev':
                    showDialog(
                      context: context,
                      builder: (context) => AlertDialog(
                        contentPadding: const EdgeInsets.fromLTRB(24, 20, 24, 24),
                        insetPadding: const EdgeInsets.symmetric(horizontal: 40, vertical: 24),
                        title: Text(
                          'عن المطور',
                          textAlign: TextAlign.center,
                          style: GoogleFonts.cairo(
                            fontSize: 26,
                            fontWeight: FontWeight.bold,
                            color: const Color(0xFF4C8C7D),
                            letterSpacing: 0.5,
                          ),
                        ),
                        content: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Container(
                              width: 180,
                              height: 180,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: const Color(0xFF4C8C7D),
                                  width: 3,
                                ),
                                image: const DecorationImage(
                                  image: AssetImage('assets/images/developer.jpg.jpg'),
                                  fit: BoxFit.contain,
                                  alignment: Alignment.center, // تعديل موضع الصورة لإظهار الوجه بشكل أفضل
                                ),
                              ),
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'عبد الرحمن جبريل',
                              textAlign: TextAlign.center,
                              style: GoogleFonts.reemKufi(
                                fontSize: 28,
                                fontWeight: FontWeight.bold,
                                color: const Color(0xFF4C8C7D),
                                height: 1.3,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'مطور تطبيقات Flutter متخصص في تطوير تطبيقات الموبايل',
                              textAlign: TextAlign.center,
                              style: GoogleFonts.tajawal(
                                fontSize: 16,
                                height: 1.3,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'يسعى دائماً لتقديم تجربة مستخدم مميزة ومفيدة',
                              textAlign: TextAlign.center,
                              style: GoogleFonts.tajawal(
                                fontSize: 16,
                                height: 1.3,
                              ),
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'بمساعدة الذكاء الاصطناعي Claude من Anthropic',
                              textAlign: TextAlign.center,
                              style: GoogleFonts.tajawal(
                                fontSize: 16,
                                fontStyle: FontStyle.italic,
                                color: Colors.grey[700],
                                height: 1.3,
                              ),
                            ),
                            const SizedBox(height: 20),
                            Text(
                              'مشاركة التطبيق صدقة جارية',
                              textAlign: TextAlign.center,
                              style: GoogleFonts.scheherazadeNew(
                                fontSize: 24,
                                color: const Color(0xFF1A237E), // لون أزرق داكن
                                fontWeight: FontWeight.bold,
                                height: 1.3,
                                letterSpacing: 0.5,
                              ),
                            ),
                            Text(
                              'شارك التطبيق مع أصدقائك',
                              textAlign: TextAlign.center,
                              style: GoogleFonts.amiri(
                                fontSize: 20,
                                color: const Color(0xFF1A237E), // لون أزرق داكن
                                fontWeight: FontWeight.bold,
                                height: 1.2,
                              ),
                            ),
                            const SizedBox(height: 16),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                ElevatedButton.icon(
                                  onPressed: () async {
                                    final Uri whatsappUrl = Uri.parse('https://wa.me/967712879614');
                                    if (await canLaunchUrl(whatsappUrl)) {
                                      await launchUrl(whatsappUrl);
                                    }
                                  },
                                  icon: const FaIcon(FontAwesomeIcons.whatsapp, color: Colors.white),
                                  label: Text(
                                    'تواصل معنا على واتساب',
                                    style: GoogleFonts.tajawal(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.white,
                                    ),
                                  ),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: const Color(0xFF25D366), // لون الواتساب
                                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 10),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                ElevatedButton.icon(
                                  onPressed: () async {
                                    final Uri phoneUrl = Uri.parse('tel:+967712879614');
                                    if (await canLaunchUrl(phoneUrl)) {
                                      await launchUrl(phoneUrl);
                                    }
                                  },
                                  icon: const Icon(Icons.phone, color: Colors.white),
                                  label: Text(
                                    'اتصل بنا',
                                    style: GoogleFonts.tajawal(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.white,
                                    ),
                                  ),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: const Color(0xFF4C8C7D),
                                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                        actions: [
                          Center(
                            child: ElevatedButton(
                              onPressed: () => Navigator.pop(context),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: const Color(0xFF4C8C7D),
                                foregroundColor: Colors.white,
                                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 10),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                              child: Text(
                                'حسناً',
                                style: GoogleFonts.tajawal(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
                    break;
                  case 'share':
                    // مشاركة التطبيق
                    break;
                }
              },
              itemBuilder:
                  (context) => [
                    const PopupMenuItem<String>(  // Add <String> type
                      value: 'dark_mode',
                      child: Row(
                        children: [
                          Icon(Icons.dark_mode, color: Color(0xFF4C8C7D)),
                          SizedBox(width: 10),
                          Text('الوضع الداكن'),
                        ],
                      ),
                    ),
                    const PopupMenuItem<String>(  // Add <String> type
                      value: 'listen',
                      child: Row(
                        children: [
                          Icon(Icons.headphones, color: Color(0xFF4C8C7D)),
                          SizedBox(width: 10),
                          Text('استمع للسور'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'about_app',
                      child: Row(
                        children: [
                          Icon(Icons.info, color: Color(0xFF4C8C7D)),
                          SizedBox(width: 10),
                          Text('لمحة عن التطبيق'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'about_dev',
                      child: Row(
                        children: [
                          Icon(Icons.person, color: Color(0xFF4C8C7D)),
                          SizedBox(width: 10),
                          Text('لمحة عن المطور'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'share',
                      child: Row(
                        children: [
                          Icon(Icons.share, color: Color(0xFF4C8C7D)),
                          SizedBox(width: 10),
                          Text('شارك التطبيق'),
                        ],
                      ),
                    ),
                  ],
            ),
          ],
        ),
        body: ListView.builder(
          itemCount: audioFiles.length,
          itemBuilder: (context, index) {
            final fileName = audioFiles[index].split('/').last;
            final surahName = audioFileToArabicName[fileName] ?? fileName;

            return Column(
              children: [
                // عنصر السورة بدون أي خلفية
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                  child: ListTile(
                    title: Text(
                      surahName,
                      style: const TextStyle(
                        color: Colors.white, // لون النص أبيض
                        fontSize: 18,
                        fontWeight: FontWeight.w500,
                      ),
                      textAlign: TextAlign.right,
                    ),
                    trailing: PopupMenuButton<String>(
                      icon: const Icon(Icons.more_vert, color: Colors.white),
                      onSelected: (value) async {
                        switch (value) {
                          case 'save_music':
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text('جاري حفظ السورة في الموسيقى...'),
                              ),
                            );
                            break;
                          case 'save_ringtone':
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text('جاري حفظ السورة كنغمة رنين...'),
                              ),
                            );
                            break;
                          case 'favorites':
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => const FavoritesScreen(),
                              ),
                            );
                            break;
                        }
                      },
                      itemBuilder: (context) => [
                        const PopupMenuItem<String>(
                          value: 'save_music',
                          child: Text('حفظ السورة في الموسيقى'),
                        ),
                        const PopupMenuItem<String>(
                          value: 'save_ringtone',
                          child: Text('حفظ السورة كنغمة رنين'),
                        ),
                        const PopupMenuItem<String>(
                          value: 'favorites',
                          child: Row(
                            children: [
                              Icon(Icons.favorite, color: Color(0xFF4C8C7D)),
                              SizedBox(width: 10),
                              Text('المفضلة'),
                            ],
                          ),
                        ),
                      ],
                    ),
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => SurahPlayerScreen(
                            audioPath: fileName,
                            surahName: surahName,
                          ),
                        ),
                      );
                    },
                  ),
                ),
                // خط فاصل بين السور
                index < audioFiles.length - 1
                    ? const Divider(color: Colors.white, height: 1, thickness: 0.5, indent: 16, endIndent: 16)
                    : const SizedBox.shrink(),
              ],
            );
          },
        ),
      ), // This closes the Scaffold
    ); // This closes the Container
  } // This closes the build method

  // Remove the unused _buildSurahTile method
  /*
  Widget _buildSurahTile(String audioFile) {
    final surahName = audioFileToArabicName[audioFile] ?? audioFile;
    return ListTile(
      title: Text(
        surahName,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 18,
        ),
        textAlign: TextAlign.right, // لمحاذاة النص إلى اليمين
      ),
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => SurahPlayerScreen(
              audioPath: audioFile,
              surahName: surahName,
            ),
          ),
        );
      },
    );
  }
  */

  Future<void> playAudio(String assetPath) async {
    try {
      // استخدام مزود الصوت المحسن لتشغيل السورة
      final audioProvider = Provider.of<BackgroundAudioProvider>(context, listen: false);
      final surahName = audioFileToArabicName[assetPath] ?? assetPath.split('.').first;

      // تشغيل السورة
      await audioProvider.playSurah(assetPath, surahName);

      // الانتقال إلى شاشة مشغل السورة
      if (mounted) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => SurahPlayerScreen(
              audioPath: assetPath,
              surahName: surahName,
            ),
          ),
        );
      }
    } catch (e) {
      debugPrint('Error playing audio: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تشغيل الملف: $assetPath')),
        );
      }
    }
  }
}

{"logs": [{"outputFile": "com.example.qorane.app-mergeReleaseResources-50:/values-te/values-te.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ab1dc0cd23ebe3c890248eaabfbb4ea4\\transformed\\jetified-media3-exoplayer-1.4.1\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,195,268,336,416,493,594,687", "endColumns": "71,67,72,67,79,76,100,92,77", "endOffsets": "122,190,263,331,411,488,589,682,760"}, "to": {"startLines": "38,39,40,41,42,43,44,45,46", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3779,3851,3919,3992,4060,4140,4217,4318,4411", "endColumns": "71,67,72,67,79,76,100,92,77", "endOffsets": "3846,3914,3987,4055,4135,4212,4313,4406,4484"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0098a6e93522fecc805d8900172003dc\\transformed\\preference-1.2.1\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,178,277,356,502,671,758", "endColumns": "72,98,78,145,168,86,83", "endOffsets": "173,272,351,497,666,753,837"}, "to": {"startLines": "36,47,51,52,55,56,57", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "3595,4489,4914,4993,5320,5489,5576", "endColumns": "72,98,78,145,168,86,83", "endOffsets": "3663,4583,4988,5134,5484,5571,5655"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3a4dcd3e8c92efa77cfa193b658940d9\\transformed\\appcompat-1.1.0\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,222,334,445,535,640,759,837,914,1005,1097,1192,1286,1387,1480,1575,1670,1761,1852,1934,2048,2150,2247,2362,2465,2580,2742,2845", "endColumns": "116,111,110,89,104,118,77,76,90,91,94,93,100,92,94,94,90,90,81,113,101,96,114,102,114,161,102,79", "endOffsets": "217,329,440,530,635,754,832,909,1000,1092,1187,1281,1382,1475,1570,1665,1756,1847,1929,2043,2145,2242,2357,2460,2575,2737,2840,2920"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,53", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,222,334,445,535,640,759,837,914,1005,1097,1192,1286,1387,1480,1575,1670,1761,1852,1934,2048,2150,2247,2362,2465,2580,2742,5139", "endColumns": "116,111,110,89,104,118,77,76,90,91,94,93,100,92,94,94,90,90,81,113,101,96,114,102,114,161,102,79", "endOffsets": "217,329,440,530,635,754,832,909,1000,1092,1187,1281,1382,1475,1570,1665,1756,1847,1929,2043,2145,2242,2357,2460,2575,2737,2840,5214"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\308a2e77faa557d0bd706972416bde6a\\transformed\\browser-1.8.0\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,166,274,385", "endColumns": "110,107,110,106", "endOffsets": "161,269,380,487"}, "to": {"startLines": "37,48,49,50", "startColumns": "4,4,4,4", "startOffsets": "3668,4588,4696,4807", "endColumns": "110,107,110,106", "endOffsets": "3774,4691,4802,4909"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8cf478dec41eed746328fa8046755ba2\\transformed\\core-1.13.1\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,265,367,468,574,681,805", "endColumns": "101,107,101,100,105,106,123,100", "endOffsets": "152,260,362,463,569,676,800,901"}, "to": {"startLines": "29,30,31,32,33,34,35,54", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2845,2947,3055,3157,3258,3364,3471,5219", "endColumns": "101,107,101,100,105,106,123,100", "endOffsets": "2942,3050,3152,3253,3359,3466,3590,5315"}}]}]}
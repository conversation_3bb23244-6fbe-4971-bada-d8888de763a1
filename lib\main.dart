import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'providers/theme_provider.dart';
import 'providers/favorites_provider.dart';
import 'providers/quran_bookmarks_provider.dart';
import 'providers/background_audio_provider.dart'; // استخدام مزود الصوت المحسن
import 'screens/home_screen.dart';
import 'screens/quran_bookmarks_screen.dart';

void main() {
  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => ThemeProvider()),
        ChangeNotifierProvider(create: (_) => FavoritesProvider()),
        ChangeNotifierProvider(create: (_) => QuranBookmarksProvider()),
        ChangeNotifierProvider(create: (_) => BackgroundAudioProvider()), // استخدام مزود الصوت المحسن
      ],
      child: const MyApp(),
    ),
  );
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return MaterialApp(
          debugShowCheckedModeBanner: false,
          theme: themeProvider.currentTheme,
          initialRoute: '/',
          routes: {
            '/': (context) => const HomeScreen(),
            '/bookmarks': (context) => const QuranBookmarksScreen(),
          },
        );
      },
    );
  }
}

{"logs": [{"outputFile": "com.example.qorane.app-mergeReleaseResources-53:/values-v24/values-v24.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3a4dcd3e8c92efa77cfa193b658940d9\\transformed\\appcompat-1.1.0\\res\\values-v24\\values-v24.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,212", "endColumns": "156,134", "endOffsets": "207,342"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2c7a28485314ee0e74d343b62dcecbb6\\transformed\\media-1.7.0\\res\\values-v24\\values-v24.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,121,182,248", "endColumns": "65,60,65,66", "endOffsets": "116,177,243,310"}, "to": {"startLines": "4,5,6,7", "startColumns": "4,4,4,4", "startOffsets": "347,413,474,540", "endColumns": "65,60,65,66", "endOffsets": "408,469,535,602"}}]}]}
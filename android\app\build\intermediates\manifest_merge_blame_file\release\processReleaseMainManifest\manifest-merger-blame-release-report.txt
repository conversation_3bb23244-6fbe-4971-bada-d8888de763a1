1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.abdulrahman.muslim_companion"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="33" />
10    <!-- إضافة أذونات الصوت والإشعارات -->
11    <uses-permission android:name="android.permission.INTERNET" />
11-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:3:5-66
11-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:3:22-64
12    <uses-permission android:name="android.permission.WAKE_LOCK" />
12-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:4:5-67
12-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:4:22-65
13    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
13-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:5:5-76
13-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:5:22-74
14    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
14-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:6:5-76
14-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:6:22-74
15    <uses-permission android:name="android.permission.VIBRATE" />
15-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:7:5-65
15-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:7:22-63
16    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
16-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:8:5-80
16-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:8:22-78
17    <!--
18         Required to query activities that can process text, see:
19         https://developer.android.com/training/package-visibility and
20         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
21
22         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
23    -->
24    <queries>
24-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:65:5-70:15
25        <intent>
25-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:66:9-69:18
26            <action android:name="android.intent.action.PROCESS_TEXT" />
26-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:67:13-72
26-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:67:21-70
27
28            <data android:mimeType="text/plain" />
28-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:68:13-50
28-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:68:19-48
29        </intent>
30    </queries>
31
32    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
32-->[androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f287878c588d535c6238ddf78c37b2ed\transformed\jetified-media3-common-1.4.1\AndroidManifest.xml:22:5-79
32-->[androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f287878c588d535c6238ddf78c37b2ed\transformed\jetified-media3-common-1.4.1\AndroidManifest.xml:22:22-76
33
34    <permission
34-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
35        android:name="com.abdulrahman.muslim_companion.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
35-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
36        android:protectionLevel="signature" />
36-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
37
38    <uses-permission android:name="com.abdulrahman.muslim_companion.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
38-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
38-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
39
40    <application
41        android:name="android.app.Application"
41-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:11:9-42
42        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
42-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
43        android:extractNativeLibs="true"
44        android:icon="@mipmap/ic_launcher"
44-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:12:9-43
45        android:label="رفيق المسلم"
45-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:10:9-36
46        android:roundIcon="@mipmap/ic_launcher" >
46-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:13:9-48
47        <activity
47-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:14:9-35:20
48            android:name="com.example.qorane.MainActivity"
48-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:15:13-41
49            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
49-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:20:13-163
50            android:exported="true"
50-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:16:13-36
51            android:hardwareAccelerated="true"
51-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:21:13-47
52            android:launchMode="singleTop"
52-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:17:13-43
53            android:taskAffinity=""
53-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:18:13-36
54            android:theme="@style/LaunchTheme"
54-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:19:13-47
55            android:windowSoftInputMode="adjustResize" >
55-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:22:13-55
56
57            <!--
58                 Specifies an Android theme to apply to this Activity as soon as
59                 the Android process has started. This theme is visible to the user
60                 while the Flutter UI initializes. After that, this theme continues
61                 to determine the Window background behind the Flutter UI.
62            -->
63            <meta-data
63-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:27:13-30:17
64                android:name="io.flutter.embedding.android.NormalTheme"
64-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:28:15-70
65                android:resource="@style/NormalTheme" />
65-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:29:15-52
66
67            <intent-filter>
67-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:31:13-34:29
68                <action android:name="android.intent.action.MAIN" />
68-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:32:17-68
68-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:32:25-66
69
70                <category android:name="android.intent.category.LAUNCHER" />
70-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:33:17-76
70-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:33:27-74
71            </intent-filter>
72        </activity>
73        <!--
74             Don't delete the meta-data below.
75             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
76        -->
77        <meta-data
77-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:38:9-40:33
78            android:name="flutterEmbedding"
78-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:39:13-44
79            android:value="2" />
79-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:40:13-30
80
81        <!-- إضافة خدمة الصوت في الخلفية -->
82        <service
82-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:43:9-50:19
83            android:name="com.ryanheise.audioservice.AudioService"
83-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:44:13-67
84            android:exported="true"
84-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:46:13-36
85            android:foregroundServiceType="mediaPlayback" >
85-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:45:13-58
86            <intent-filter>
86-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:47:13-49:29
87                <action android:name="android.media.browse.MediaBrowserService" />
87-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:48:17-83
87-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:48:25-80
88            </intent-filter>
89        </service>
90
91        <receiver
91-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:52:9-58:20
92            android:name="com.ryanheise.audioservice.MediaButtonReceiver"
92-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:53:13-74
93            android:exported="true" >
93-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:54:13-36
94            <intent-filter>
94-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:55:13-57:29
95                <action android:name="android.intent.action.MEDIA_BUTTON" />
95-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:56:17-77
95-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:56:25-74
96            </intent-filter>
97        </receiver>
98        <!--
99           Declares a provider which allows us to store files to share in
100           '.../caches/share_plus' and grant the receiving action access
101        -->
102        <provider
102-->[:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-7.2.2\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:9-21:20
103            android:name="dev.fluttercommunity.plus.share.ShareFileProvider"
103-->[:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-7.2.2\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:13-77
104            android:authorities="com.abdulrahman.muslim_companion.flutter.share_provider"
104-->[:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-7.2.2\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:13-74
105            android:exported="false"
105-->[:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-7.2.2\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:13-37
106            android:grantUriPermissions="true" >
106-->[:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-7.2.2\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:17:13-47
107            <meta-data
107-->[:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-7.2.2\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:18:13-20:68
108                android:name="android.support.FILE_PROVIDER_PATHS"
108-->[:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-7.2.2\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:19:17-67
109                android:resource="@xml/flutter_share_file_paths" />
109-->[:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-7.2.2\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:17-65
110        </provider>
111        <!--
112           This manifest declared broadcast receiver allows us to use an explicit
113           Intent when creating a PendingItent to be informed of the user's choice
114        -->
115        <receiver
115-->[:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-7.2.2\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:26:9-32:20
116            android:name="dev.fluttercommunity.plus.share.SharePlusPendingIntent"
116-->[:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-7.2.2\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:27:13-82
117            android:exported="false" >
117-->[:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-7.2.2\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:13-37
118            <intent-filter>
118-->[:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-7.2.2\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:29:13-31:29
119                <action android:name="EXTRA_CHOSEN_COMPONENT" />
119-->[:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-7.2.2\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:30:17-65
119-->[:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-7.2.2\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:30:25-62
120            </intent-filter>
121        </receiver>
122
123        <activity
123-->[:url_launcher_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.16\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-11:74
124            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
124-->[:url_launcher_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.16\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-74
125            android:exported="false"
125-->[:url_launcher_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.16\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-37
126            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
126-->[:url_launcher_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.16\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-71
127
128        <provider
128-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
129            android:name="androidx.startup.InitializationProvider"
129-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
130            android:authorities="com.abdulrahman.muslim_companion.androidx-startup"
130-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
131            android:exported="false" >
131-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
132            <meta-data
132-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
133                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
133-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
134                android:value="androidx.startup" />
134-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
135            <meta-data
135-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
136                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
136-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
137                android:value="androidx.startup" />
137-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
138        </provider>
139
140        <uses-library
140-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
141            android:name="androidx.window.extensions"
141-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
142            android:required="false" />
142-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
143        <uses-library
143-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
144            android:name="androidx.window.sidecar"
144-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
145            android:required="false" />
145-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
146
147        <receiver
147-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
148            android:name="androidx.profileinstaller.ProfileInstallReceiver"
148-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
149            android:directBootAware="false"
149-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
150            android:enabled="true"
150-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
151            android:exported="true"
151-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
152            android:permission="android.permission.DUMP" >
152-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
153            <intent-filter>
153-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
154                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
154-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
154-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
155            </intent-filter>
156            <intent-filter>
156-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
157                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
157-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
157-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
158            </intent-filter>
159            <intent-filter>
159-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
160                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
160-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
160-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
161            </intent-filter>
162            <intent-filter>
162-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
163                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
163-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
163-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
164            </intent-filter>
165        </receiver>
166    </application>
167
168</manifest>

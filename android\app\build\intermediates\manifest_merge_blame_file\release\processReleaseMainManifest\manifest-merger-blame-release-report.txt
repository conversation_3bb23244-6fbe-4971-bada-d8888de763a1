1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.abdulrahman.muslim_companion"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="33" />
10    <!-- إضافة أذونات الصوت -->
11    <uses-permission android:name="android.permission.INTERNET" />
11-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:3:5-66
11-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:3:22-64
12    <uses-permission android:name="android.permission.WAKE_LOCK" />
12-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:4:5-67
12-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:4:22-65
13    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
13-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:5:5-76
13-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:5:22-74
14    <!--
15         Required to query activities that can process text, see:
16         https://developer.android.com/training/package-visibility and
17         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
18
19         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
20    -->
21    <queries>
21-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:62:5-67:15
22        <intent>
22-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:63:9-66:18
23            <action android:name="android.intent.action.PROCESS_TEXT" />
23-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:64:13-72
23-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:64:21-70
24
25            <data android:mimeType="text/plain" />
25-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:65:13-50
25-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:65:19-48
26        </intent>
27    </queries>
28
29    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
29-->[androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f287878c588d535c6238ddf78c37b2ed\transformed\jetified-media3-common-1.4.1\AndroidManifest.xml:22:5-79
29-->[androidx.media3:media3-common:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f287878c588d535c6238ddf78c37b2ed\transformed\jetified-media3-common-1.4.1\AndroidManifest.xml:22:22-76
30
31    <permission
31-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
32        android:name="com.abdulrahman.muslim_companion.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
32-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
33        android:protectionLevel="signature" />
33-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
34
35    <uses-permission android:name="com.abdulrahman.muslim_companion.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
35-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
35-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
36
37    <application
38        android:name="android.app.Application"
38-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:8:9-42
39        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
39-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8cf478dec41eed746328fa8046755ba2\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
40        android:extractNativeLibs="true"
41        android:icon="@mipmap/ic_launcher"
41-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:9:9-43
42        android:label="رفيق المسلم"
42-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:7:9-36
43        android:roundIcon="@mipmap/ic_launcher" >
43-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:10:9-48
44        <activity
44-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:11:9-32:20
45            android:name="com.example.qorane.MainActivity"
45-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:12:13-41
46            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
46-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:17:13-163
47            android:exported="true"
47-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:13:13-36
48            android:hardwareAccelerated="true"
48-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:18:13-47
49            android:launchMode="singleTop"
49-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:14:13-43
50            android:taskAffinity=""
50-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:15:13-36
51            android:theme="@style/LaunchTheme"
51-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:16:13-47
52            android:windowSoftInputMode="adjustResize" >
52-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:19:13-55
53
54            <!--
55                 Specifies an Android theme to apply to this Activity as soon as
56                 the Android process has started. This theme is visible to the user
57                 while the Flutter UI initializes. After that, this theme continues
58                 to determine the Window background behind the Flutter UI.
59            -->
60            <meta-data
60-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:24:13-27:17
61                android:name="io.flutter.embedding.android.NormalTheme"
61-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:25:15-70
62                android:resource="@style/NormalTheme" />
62-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:26:15-52
63
64            <intent-filter>
64-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:28:13-31:29
65                <action android:name="android.intent.action.MAIN" />
65-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:29:17-68
65-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:29:25-66
66
67                <category android:name="android.intent.category.LAUNCHER" />
67-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:30:17-76
67-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:30:27-74
68            </intent-filter>
69        </activity>
70        <!--
71             Don't delete the meta-data below.
72             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
73        -->
74        <meta-data
74-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:35:9-37:33
75            android:name="flutterEmbedding"
75-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:36:13-44
76            android:value="2" />
76-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:37:13-30
77
78        <!-- إضافة خدمة الصوت في الخلفية -->
79        <service
79-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:40:9-47:19
80            android:name="com.ryanheise.audioservice.AudioService"
80-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:41:13-67
81            android:exported="true"
81-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:43:13-36
82            android:foregroundServiceType="mediaPlayback" >
82-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:42:13-58
83            <intent-filter>
83-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:44:13-46:29
84                <action android:name="android.media.browse.MediaBrowserService" />
84-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:45:17-83
84-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:45:25-80
85            </intent-filter>
86        </service>
87
88        <receiver
88-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:49:9-55:20
89            android:name="com.ryanheise.audioservice.MediaButtonReceiver"
89-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:50:13-74
90            android:exported="true" >
90-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:51:13-36
91            <intent-filter>
91-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:52:13-54:29
92                <action android:name="android.intent.action.MEDIA_BUTTON" />
92-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:53:17-77
92-->E:\Qorane\qorane\android\app\src\main\AndroidManifest.xml:53:25-74
93            </intent-filter>
94        </receiver>
95        <!--
96           Declares a provider which allows us to store files to share in
97           '.../caches/share_plus' and grant the receiving action access
98        -->
99        <provider
99-->[:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-7.2.2\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:9-21:20
100            android:name="dev.fluttercommunity.plus.share.ShareFileProvider"
100-->[:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-7.2.2\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:13-77
101            android:authorities="com.abdulrahman.muslim_companion.flutter.share_provider"
101-->[:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-7.2.2\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:13-74
102            android:exported="false"
102-->[:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-7.2.2\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:13-37
103            android:grantUriPermissions="true" >
103-->[:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-7.2.2\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:17:13-47
104            <meta-data
104-->[:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-7.2.2\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:18:13-20:68
105                android:name="android.support.FILE_PROVIDER_PATHS"
105-->[:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-7.2.2\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:19:17-67
106                android:resource="@xml/flutter_share_file_paths" />
106-->[:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-7.2.2\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:17-65
107        </provider>
108        <!--
109           This manifest declared broadcast receiver allows us to use an explicit
110           Intent when creating a PendingItent to be informed of the user's choice
111        -->
112        <receiver
112-->[:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-7.2.2\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:26:9-32:20
113            android:name="dev.fluttercommunity.plus.share.SharePlusPendingIntent"
113-->[:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-7.2.2\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:27:13-82
114            android:exported="false" >
114-->[:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-7.2.2\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:13-37
115            <intent-filter>
115-->[:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-7.2.2\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:29:13-31:29
116                <action android:name="EXTRA_CHOSEN_COMPONENT" />
116-->[:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-7.2.2\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:30:17-65
116-->[:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-7.2.2\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:30:25-62
117            </intent-filter>
118        </receiver>
119
120        <activity
120-->[:url_launcher_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.16\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-11:74
121            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
121-->[:url_launcher_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.16\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-74
122            android:exported="false"
122-->[:url_launcher_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.16\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-37
123            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
123-->[:url_launcher_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.16\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-71
124
125        <provider
125-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
126            android:name="androidx.startup.InitializationProvider"
126-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
127            android:authorities="com.abdulrahman.muslim_companion.androidx-startup"
127-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
128            android:exported="false" >
128-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
129            <meta-data
129-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
130                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
130-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
131                android:value="androidx.startup" />
131-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e80aa748ff6540407d8dba61c0d945fe\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
132            <meta-data
132-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
133                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
133-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
134                android:value="androidx.startup" />
134-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
135        </provider>
136
137        <uses-library
137-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
138            android:name="androidx.window.extensions"
138-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
139            android:required="false" />
139-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
140        <uses-library
140-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
141            android:name="androidx.window.sidecar"
141-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
142            android:required="false" />
142-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5093ab42d2307deb2d7ac0b7f5718c38\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
143
144        <receiver
144-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
145            android:name="androidx.profileinstaller.ProfileInstallReceiver"
145-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
146            android:directBootAware="false"
146-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
147            android:enabled="true"
147-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
148            android:exported="true"
148-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
149            android:permission="android.permission.DUMP" >
149-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
150            <intent-filter>
150-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
151                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
151-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
151-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
152            </intent-filter>
153            <intent-filter>
153-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
154                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
154-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
154-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
155            </intent-filter>
156            <intent-filter>
156-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
157                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
157-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
157-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
158            </intent-filter>
159            <intent-filter>
159-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
160                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
160-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
160-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5f7dcf6815bacc3ae7f099ba801348de\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
161            </intent-filter>
162        </receiver>
163    </application>
164
165</manifest>

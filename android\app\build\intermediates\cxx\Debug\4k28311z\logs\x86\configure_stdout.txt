Not searching for unused variables given on the command line.
-- Android: Targeting API '21' with architecture 'x86', ABI 'x86', and processor 'i686'
-- Android: Selected unified Clang toolchain
-- The C compiler identification is Clang 12.0.8
-- The CXX compiler identification is Clang 12.0.8
-- Detecting C compiler <PERSON><PERSON> info
-- Detecting C compiler AB<PERSON> info - done
-- Check for working C compiler: C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/23.1.7779620/toolchains/llvm/prebuilt/windows-x86_64/bin/clang.exe - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler AB<PERSON> info
-- Detecting CXX compiler AB<PERSON> info - done
-- Check for working CXX compiler: C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/23.1.7779620/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Configuring done
-- Generating done
-- Build files have been written to: E:/Qorane/qorane/android/app/.cxx/Debug/4k28311z/x86

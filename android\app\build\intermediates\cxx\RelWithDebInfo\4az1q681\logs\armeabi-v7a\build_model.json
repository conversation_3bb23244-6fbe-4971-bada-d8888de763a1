{"info": {"name": "armeabi-v7a", "bitness": 32, "isDefault": true, "isDeprecated": false, "architecture": "arm", "triple": "arm-linux-androideabi", "llvmTriple": "armv7-none-linux-androideabi"}, "cxxBuildFolder": "E:\\Qorane\\qorane\\android\\app\\.cxx\\RelWithDebInfo\\4az1q681\\armeabi-v7a", "soFolder": "E:\\Qorane\\qorane\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\4az1q681\\obj\\armeabi-v7a", "soRepublishFolder": "E:\\Qorane\\qorane\\android\\app\\build\\intermediates\\cmake\\release\\obj\\armeabi-v7a", "abiPlatformVersion": 21, "cmake": {"effectiveConfiguration": {"inheritEnvironments": [], "variables": []}}, "variant": {"buildSystemArgumentList": ["-Wno-dev", "--no-warn-unused-cli"], "cFlagsList": [], "cppFlagsList": [], "variantName": "release", "isDebuggableEnabled": false, "validAbiList": ["armeabi-v7a", "arm64-v8a", "x86", "x86_64"], "buildTargetSet": [], "implicitBuildTargetSet": [], "cmakeSettingsConfiguration": "android-gradle-plugin-predetermined-name", "module": {"cxxFolder": "E:\\Qorane\\qorane\\android\\app\\.cxx", "intermediatesBaseFolder": "E:\\Qorane\\qorane\\android\\app\\build\\intermediates", "intermediatesFolder": "E:\\Qorane\\qorane\\android\\app\\build\\intermediates\\cxx", "gradleModulePathName": ":app", "moduleRootFolder": "E:\\Qorane\\qorane\\android\\app", "moduleBuildFile": "E:\\Qorane\\qorane\\android\\app\\build.gradle.kts", "makeFile": "C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt", "buildSystem": "CMAKE", "ndkFolder": "C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\ndk\\23.1.7779620", "ndkFolderBeforeSymLinking": "C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\ndk\\23.1.7779620", "ndkVersion": "23.1.7779620", "ndkSupportedAbiList": ["armeabi-v7a", "arm64-v8a", "x86", "x86_64"], "ndkDefaultAbiList": ["armeabi-v7a", "arm64-v8a", "x86", "x86_64"], "ndkDefaultStl": "LIBCXX_STATIC", "ndkMetaPlatforms": {"min": 16, "max": 31, "aliases": {"20": 19, "25": 24, "J": 16, "J-MR1": 17, "J-MR2": 18, "K": 19, "L": 21, "L-MR1": 22, "M": 23, "N": 24, "N-MR1": 24, "O": 26, "O-MR1": 27, "P": 28, "Q": 29, "R": 30, "S": 31}}, "ndkMetaAbiList": [{"name": "armeabi-v7a", "bitness": 32, "isDefault": true, "isDeprecated": false, "architecture": "arm", "triple": "arm-linux-androideabi", "llvmTriple": "armv7-none-linux-androideabi"}, {"name": "arm64-v8a", "bitness": 64, "isDefault": true, "isDeprecated": false, "architecture": "arm64", "triple": "aarch64-linux-android", "llvmTriple": "aarch64-none-linux-android"}, {"name": "x86", "bitness": 32, "isDefault": true, "isDeprecated": false, "architecture": "x86", "triple": "i686-linux-android", "llvmTriple": "i686-none-linux-android"}, {"name": "x86_64", "bitness": 64, "isDefault": true, "isDeprecated": false, "architecture": "x86_64", "triple": "x86_64-linux-android", "llvmTriple": "x86_64-none-linux-android"}], "cmakeToolchainFile": "C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\ndk\\23.1.7779620\\build\\cmake\\android.toolchain.cmake", "cmake": {"cmakeExe": "C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\cmake.exe"}, "stlSharedObjectMap": {"LIBCXX_SHARED": {"armeabi-v7a": "C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\ndk\\23.1.7779620\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\arm-linux-androideabi\\libc++_shared.so", "arm64-v8a": "C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\ndk\\23.1.7779620\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\aarch64-linux-android\\libc++_shared.so", "x86": "C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\ndk\\23.1.7779620\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\i686-linux-android\\libc++_shared.so", "x86_64": "C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\ndk\\23.1.7779620\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\x86_64-linux-android\\libc++_shared.so"}, "LIBCXX_STATIC": {}, "NONE": {}, "SYSTEM": {}}, "project": {"rootBuildGradleFolder": "E:\\Qorane\\qorane\\android", "sdkFolder": "C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk", "isBuildOnlyTargetAbiEnabled": true, "isCmakeBuildCohabitationEnabled": false, "isPrefabEnabled": false}, "outputOptions": [], "ninjaExe": "C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "hasBuildTimeInformation": true}, "prefabClassPaths": [], "prefabPackages": [], "prefabPackageConfigurations": [], "stlType": "c++_static", "optimizationTag": "RelWithDebInfo"}, "buildSettings": {"environmentVariables": []}, "prefabFolder": "E:\\Qorane\\qorane\\android\\app\\.cxx\\RelWithDebInfo\\4az1q681\\prefab\\armeabi-v7a", "isActiveAbi": true, "fullConfigurationHash": "4az1q681r3s1v6p1v5c4l4e1e51182z1w4a1v6r5e4os2q4z632h263s63ft", "fullConfigurationHashKey": "# Values used to calculate the hash in this folder name.\n# Should not depend on the absolute path of the project itself.\n#   - AGP: 8.7.0.\n#   - $NDK is the path to NDK 23.1.7779620.\n#   - $PROJECT is the path to the parent folder of the root Gradle build file.\n#   - $ABI is the ABI to be built with. The specific value doesn't contribute to the value of the hash.\n#   - $HASH is the hash value computed from this text.\n#   - $CMAKE is the path to CMake 3.22.1.\n#   - $NINJA is the path to Ninja.\n-HC:/flutter/packages/flutter_tools/gradle/src/main/groovy\n-DCMAKE_SYSTEM_NAME=Android\n-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\n-DCMAKE_SYSTEM_VERSION=21\n-DANDROID_PLATFORM=android-21\n-DANDROID_ABI=$ABI\n-DCMAKE_ANDROID_ARCH_ABI=$ABI\n-DANDROID_NDK=$NDK\n-DCMAKE_ANDROID_NDK=$NDK\n-DCMAKE_TOOLCHAIN_FILE=$NDK/build/cmake/android.toolchain.cmake\n-DCMAKE_MAKE_PROGRAM=$NINJA\n-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=$PROJECT/app/build/intermediates/cxx/RelWithDebInfo/$HASH/obj/$ABI\n-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=$PROJECT/app/build/intermediates/cxx/RelWithDebInfo/$HASH/obj/$ABI\n-DCMAKE_BUILD_TYPE=RelWithDebInfo\n-B$PROJECT/app/.cxx/RelWithDebInfo/$HASH/$ABI\n-GNinja\n-Wno-dev\n--no-warn-unused-cli", "configurationArguments": ["-HC:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy", "-DCMAKE_SYSTEM_NAME=Android", "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON", "-DCMAKE_SYSTEM_VERSION=21", "-DANDROID_PLATFORM=android-21", "-DANDROID_ABI=armeabi-v7a", "-DCMAKE_ANDROID_ARCH_ABI=armeabi-v7a", "-DANDROID_NDK=C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\ndk\\23.1.7779620", "-DCMAKE_ANDROID_NDK=C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\ndk\\23.1.7779620", "-DCMAKE_TOOLCHAIN_FILE=C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\ndk\\23.1.7779620\\build\\cmake\\android.toolchain.cmake", "-DCMAKE_MAKE_PROGRAM=C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=E:\\Qorane\\qorane\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\4az1q681\\obj\\armeabi-v7a", "-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=E:\\Qorane\\qorane\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\4az1q681\\obj\\armeabi-v7a", "-DCMAKE_BUILD_TYPE=RelWithDebInfo", "-BE:\\Qorane\\qorane\\android\\app\\.cxx\\RelWithDebInfo\\4az1q681\\armeabi-v7a", "-<PERSON><PERSON><PERSON><PERSON>", "-Wno-dev", "--no-warn-unused-cli"], "intermediatesParentFolder": "E:\\Qorane\\qorane\\android\\app\\build\\intermediates\\cxx\\RelWithDebInfo\\4az1q681"}
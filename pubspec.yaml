name: muslim_companion
description: "تطبيق رفيق المسلم - تطبيق شامل للمسلم"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.7.2

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  share_plus: ^7.2.1
  flutter_localizations:
    sdk: flutter
  google_fonts: ^6.2.1
  path_provider: ^2.1.5
  cupertino_icons: ^1.0.2
  provider: ^6.1.4
  shared_preferences: ^2.5.3
  url_launcher: ^6.2.5
  font_awesome_flutter: ^10.7.0
  http: ^1.2.0
  path: ^1.8.3
  just_audio: ^0.9.36
  flutter_tts: ^4.0.2
  syncfusion_flutter_pdfviewer: ^26.2.14
  audio_service: ^0.18.18
  flutter_local_notifications: ^17.2.3

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_launcher_icons: ^0.13.1
  flutter_lints: ^5.0.0

# Flutter Launcher Icons configuration
flutter_launcher_icons:
  android: true
  ios: true
  image_path: "assets/images/icon.png"
  adaptive_icon_background: "#ffffff"
  adaptive_icon_foreground: "assets/images/icon.png"
  remove_alpha_ios: true

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  uses-material-design: true
  assets:
    - assets/images/
    - assets/audio/
    - assets/audio/allah_names/
    - assets/pdf/
    - assets/database/
    - assets/fonts/
    - assets/images/icon.ico

  # fonts:
  #   - family: Amiri
  #     fonts:
  #       - asset: assets/fonts/Amiri-Regular.ttf
  #       - asset: assets/fonts/Amiri-Bold.ttf
  #         weight: 700

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 10ms
    create-module-model completed in 19ms
    [gap of 16ms]
    create-ARM64_V8A-model 14ms
    create-X86_64-model 16ms
    create-module-model 20ms
    [gap of 31ms]
    create-X86_64-model 13ms
    create-module-model 18ms
    [gap of 20ms]
    create-X86-model 29ms
    [gap of 10ms]
  create-initial-cxx-model completed in 218ms
create_cxx_tasks completed in 224ms


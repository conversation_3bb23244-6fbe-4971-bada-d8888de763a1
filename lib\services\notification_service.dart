import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter/material.dart';

class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  final FlutterLocalNotificationsPlugin _notificationsPlugin = 
      FlutterLocalNotificationsPlugin();

  // تهيئة خدمة الإشعارات
  Future<void> initialize() async {
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');
    
    const InitializationSettings initializationSettings =
        InitializationSettings(android: initializationSettingsAndroid);
    
    await _notificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    // طلب الأذونات للإشعارات
    await _requestPermissions();
  }

  // طلب أذونات الإشعارات
  Future<void> _requestPermissions() async {
    final AndroidFlutterLocalNotificationsPlugin? androidImplementation =
        _notificationsPlugin.resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>();

    if (androidImplementation != null) {
      await androidImplementation.requestNotificationsPermission();
    }
  }

  // معالج النقر على الإشعار
  void _onNotificationTapped(NotificationResponse response) {
    debugPrint('Notification tapped: ${response.payload}');
    
    // معالجة الإجراءات المختلفة
    switch (response.actionId) {
      case 'play_pause':
        _handlePlayPause();
        break;
      case 'next':
        _handleNext();
        break;
      case 'repeat':
        _handleRepeat();
        break;
      default:
        // النقر على الإشعار نفسه - العودة إلى التطبيق
        _handleNotificationTap();
        break;
    }
  }

  // معالج تشغيل/إيقاف
  void _handlePlayPause() {
    debugPrint('Play/Pause action triggered from notification');
    // سيتم ربطها بالمزود لاحقاً
  }

  // معالج التالي
  void _handleNext() {
    debugPrint('Next action triggered from notification');
    // سيتم ربطها بالمزود لاحقاً
  }

  // معالج التكرار
  void _handleRepeat() {
    debugPrint('Repeat action triggered from notification');
    // سيتم ربطها بالمزود لاحقاً
  }

  // معالج النقر على الإشعار
  void _handleNotificationTap() {
    debugPrint('Notification tapped - should return to app');
    // سيتم ربطها بالتطبيق لاحقاً
  }

  // إظهار إشعار التشغيل
  Future<void> showPlayingNotification({
    required String title,
    required String surahName,
    required bool isPlaying,
    required bool isLooping,
  }) async {
    final AndroidNotificationDetails androidPlatformChannelSpecifics =
        AndroidNotificationDetails(
      'quran_player',
      'مشغل القرآن',
      channelDescription: 'إشعارات مشغل القرآن الكريم',
      importance: Importance.low,
      priority: Priority.low,
      ongoing: true,
      autoCancel: false,
      playSound: false,
      enableVibration: false,
      showWhen: false,
      actions: <AndroidNotificationAction>[
        AndroidNotificationAction(
          'play_pause',
          isPlaying ? 'إيقاف مؤقت' : 'تشغيل',
        ),
        AndroidNotificationAction(
          'next',
          'التالي',
        ),
        AndroidNotificationAction(
          'repeat',
          isLooping ? 'إلغاء التكرار' : 'تكرار',
        ),
      ],
    );

    final NotificationDetails platformChannelSpecifics =
        NotificationDetails(android: androidPlatformChannelSpecifics);

    await _notificationsPlugin.show(
      0,
      title,
      '${isPlaying ? "يتم تشغيل" : "متوقف مؤقتاً"}: $surahName',
      platformChannelSpecifics,
      payload: 'quran_player',
    );
  }

  // إخفاء الإشعار
  Future<void> hideNotification() async {
    await _notificationsPlugin.cancel(0);
  }

  // إظهار إشعار بسيط
  Future<void> showSimpleNotification({
    required String title,
    required String body,
  }) async {
    const AndroidNotificationDetails androidPlatformChannelSpecifics =
        AndroidNotificationDetails(
      'simple_notifications',
      'إشعارات عامة',
      channelDescription: 'إشعارات عامة للتطبيق',
      importance: Importance.defaultImportance,
      priority: Priority.defaultPriority,
    );

    const NotificationDetails platformChannelSpecifics =
        NotificationDetails(android: androidPlatformChannelSpecifics);

    await _notificationsPlugin.show(
      1,
      title,
      body,
      platformChannelSpecifics,
    );
  }
}

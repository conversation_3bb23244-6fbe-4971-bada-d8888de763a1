[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.qorane.app-debug-55:\\mipmap-hdpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.qorane.app-main-57:\\mipmap-hdpi\\ic_launcher.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.qorane.app-debug-55:\\mipmap-xxxhdpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.qorane.app-main-57:\\mipmap-xxxhdpi\\ic_launcher.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.qorane.app-debug-55:\\mipmap-mdpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.qorane.app-main-57:\\mipmap-mdpi\\ic_launcher.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.qorane.app-debug-55:\\drawable_app_icon.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.qorane.app-main-57:\\drawable\\app_icon.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.qorane.app-debug-55:\\mipmap-xxhdpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.qorane.app-main-57:\\mipmap-xxhdpi\\ic_launcher.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.qorane.app-debug-55:\\drawable-v21_launch_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.qorane.app-main-57:\\drawable-v21\\launch_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.qorane.app-debug-55:\\drawable_icon.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.qorane.app-main-57:\\drawable\\icon.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.qorane.app-debug-55:\\mipmap-xhdpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.example.qorane.app-main-57:\\mipmap-xhdpi\\ic_launcher.png"}]
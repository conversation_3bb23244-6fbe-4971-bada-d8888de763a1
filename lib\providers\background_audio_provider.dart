import 'package:flutter/material.dart';
import 'package:just_audio/just_audio.dart';
import '../data/surah_names.dart';
import '../services/notification_service.dart';

class BackgroundAudioProvider with ChangeNotifier {
  // مشغل الصوت
  final AudioPlayer _audioPlayer = AudioPlayer();

  // خدمة الإشعارات
  final NotificationService _notificationService = NotificationService();

  // حالة التشغيل
  bool _isPlaying = false;
  bool get isPlaying => _isPlaying;

  // حالة التكرار
  bool _isLooping = false;
  bool get isLooping => _isLooping;

  // مسار الملف الحالي
  String? _currentAudioPath;
  String? get currentAudioPath => _currentAudioPath;

  // اسم السورة الحالية
  String _currentSurahName = '';
  String get currentSurahName => _currentSurahName;

  // مدة الملف الصوتي
  Duration _duration = Duration.zero;
  Duration get duration => _duration;

  // موضع التشغيل الحالي
  Duration _position = Duration.zero;
  Duration get position => _position;

  // قائمة ملفات الصوت
  final List<String> _audioFiles = [
    'Abasa.m4a', 'AL adeat.m4a', 'AL ahkaf.m4a', 'AL ankabot-Alqare Abdulrahman Mosed.m4a',
    'AL asr.m4a', 'AL baenah.m4a', 'AL balad.m4a', 'AL borog.m4a', 'AL dareat.m4a',
    'AL doha.m4a', 'AL eklas.m4a', 'AL enfetar.m4a', 'AL ensan.m4a', 'AL enshekak.m4a',
    'AL fager.m4a', 'AL falak.m4a', 'AL fatehah.m4a', 'AL fath.m4a', 'AL feel.m4a',
    'AL forkan.m4a', 'AL gasheah.m4a', 'AL gateah.m4a', 'AL gomah.m4a', 'AL haag.m4a',
    'AL haded.m4a', 'AL hakah.m4a', 'AL hashr.m4a', 'AL hgrat.m4a', 'AL homazah.m4a',
    'AL kadr.m4a', 'AL kamar.m4a', 'AL kaotar.m4a', 'AL kareah.m4a', 'AL keamah.m4a',
    'AL lael.m4a', 'AL maareg.m4a', 'AL maedah.m4a', 'AL maon.m4a', 'AL modater.m4a',
    'AL mogadalh.m4a', 'AL momenon.m4a', 'AL momtahnah.m4a', 'AL monafkon.m4a',
    'AL morsalat.m4a', 'AL motafefen.m4a', 'AL mozamel.m4a', 'AL nabaa.m4a', 'AL nagm.m4a',
    'AL naml.m4a', 'AL nas.m4a', 'AL nasr.m4a', 'AL noor.m4a', 'AL oakeah.m4a',
    'AL rahman.m4a', 'AL saf.m4a', 'AL safat.m4a', 'AL shams.m4a', 'AL sharh.m4a',
    'AL shoara.m4a', 'AL tahrem.m4a', 'AL takator.m4a', 'AL takoer.m4a', 'AL talak.m4a',
    'AL tarek.m4a', 'AL teen.m4a', 'AL tgabon.m4a', 'AL toor.m4a', 'AL zolzolah.m4a',
    'AL zomor.m4a', 'ALamran.m4a', 'Doaa AL htm.m4a', 'Ekraa.m4a', 'Mohamad.m4a',
    'Nooh.m4a', 'Qaf.m4a', 'Qoraesh.m4a', 'Tabat.m4a'
  ];
  List<String> get audioFiles => _audioFiles;

  // الباني
  BackgroundAudioProvider() {
    _initializeNotifications();
    _setupAudioPlayer();
  }

  // تهيئة الإشعارات
  Future<void> _initializeNotifications() async {
    await _notificationService.initialize();
  }

  // إعداد مشغل الصوت
  void _setupAudioPlayer() {
    // الاستماع إلى حالة التشغيل
    _audioPlayer.playerStateStream.listen((state) {
      _isPlaying = state.playing;
      _updateNotification();
      notifyListeners();
    });

    // الاستماع إلى مدة الملف الصوتي
    _audioPlayer.durationStream.listen((d) {
      _duration = d ?? Duration.zero;
      notifyListeners();
    });

    // الاستماع إلى موضع التشغيل الحالي
    _audioPlayer.positionStream.listen((p) {
      _position = p;
      notifyListeners();
    });

    // الاستماع إلى حالة المعالجة
    _audioPlayer.processingStateStream.listen((state) {
      if (state == ProcessingState.completed) {
        if (_isLooping) {
          // إذا كان التكرار مفعلاً، أعد تشغيل نفس السورة
          _audioPlayer.seek(Duration.zero);
          _audioPlayer.play();
          _isPlaying = true;
          _updateNotification();
          notifyListeners();
        } else {
          // إذا كان التكرار غير مفعل، انتقل إلى السورة التالية
          playNext();
        }
      }
    });
  }

  // تشغيل سورة
  Future<void> playSurah(String audioPath, String surahName) async {
    try {
      // تحديد المسار الكامل
      String fullPath = audioPath;
      if (!fullPath.startsWith('assets/audio/')) {
        fullPath = 'assets/audio/$fullPath';
      }

      // طباعة معلومات التشغيل
      debugPrint('Playing surah: $surahName');
      debugPrint('Audio path: $fullPath');

      // تعيين مصدر الصوت
      await _audioPlayer.setAsset(fullPath);

      // تخزين معلومات السورة الحالية
      _currentAudioPath = audioPath;
      _currentSurahName = surahName;

      // تشغيل الصوت
      await _audioPlayer.play();

      // تحديث حالة التشغيل
      _isPlaying = true;
      
      // إظهار الإشعار
      await _updateNotification();
      
      notifyListeners();
    } catch (e) {
      debugPrint('Error playing surah: $e');
      _isPlaying = false;
      notifyListeners();
    }
  }

  // تحديث الإشعار
  Future<void> _updateNotification() async {
    if (_currentSurahName.isNotEmpty) {
      await _notificationService.showPlayingNotification(
        title: 'رفيق المسلم',
        surahName: _currentSurahName,
        isPlaying: _isPlaying,
        isLooping: _isLooping,
      );
    }
  }

  // إيقاف التشغيل مؤقتًا
  Future<void> pause() async {
    if (_isPlaying) {
      await _audioPlayer.pause();
      _isPlaying = false;
      await _updateNotification();
      notifyListeners();
    }
  }

  // استئناف التشغيل
  Future<void> resume() async {
    if (!_isPlaying && _currentAudioPath != null) {
      await _audioPlayer.play();
      _isPlaying = true;
      await _updateNotification();
      notifyListeners();
    }
  }

  // التبديل بين التشغيل والإيقاف المؤقت
  void togglePlayPause() {
    if (_isPlaying) {
      pause();
    } else {
      resume();
    }
  }

  // الانتقال إلى موضع محدد
  Future<void> seek(Duration position) async {
    await _audioPlayer.seek(position);
  }

  // تشغيل السورة التالية
  void playNext() {
    if (_currentAudioPath == null) return;

    int currentIndex = _audioFiles.indexOf(_currentAudioPath!);
    if (currentIndex == -1) currentIndex = -1;

    String nextAudioPath;
    String nextSurahName;

    if (currentIndex < _audioFiles.length - 1) {
      nextAudioPath = _audioFiles[currentIndex + 1];
    } else {
      nextAudioPath = _audioFiles[0];
    }

    nextSurahName = audioFileToArabicName[nextAudioPath] ?? nextAudioPath.split('.').first;
    playSurah(nextAudioPath, nextSurahName);
  }

  // تشغيل السورة السابقة
  void playPrevious() {
    if (_currentAudioPath == null) return;

    int currentIndex = _audioFiles.indexOf(_currentAudioPath!);
    if (currentIndex == -1) currentIndex = 1;

    String previousAudioPath;
    String previousSurahName;

    if (currentIndex > 0) {
      previousAudioPath = _audioFiles[currentIndex - 1];
    } else {
      previousAudioPath = _audioFiles[_audioFiles.length - 1];
    }

    previousSurahName = audioFileToArabicName[previousAudioPath] ?? previousAudioPath.split('.').first;
    playSurah(previousAudioPath, previousSurahName);
  }

  // تبديل حالة التكرار
  void toggleLooping() {
    _isLooping = !_isLooping;
    _updateNotification();
    notifyListeners();
  }

  // إيقاف التشغيل وإخفاء الإشعار
  Future<void> stop() async {
    await _audioPlayer.stop();
    await _notificationService.hideNotification();
    _isPlaying = false;
    _currentAudioPath = null;
    _currentSurahName = '';
    notifyListeners();
  }

  // تنسيق الوقت
  String formatTime(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }

  // التخلص من الموارد
  @override
  void dispose() {
    _audioPlayer.dispose();
    _notificationService.hideNotification();
    super.dispose();
  }
}

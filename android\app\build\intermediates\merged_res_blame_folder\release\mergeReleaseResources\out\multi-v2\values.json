{"logs": [{"outputFile": "com.example.qorane.app-mergeReleaseResources-50:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3a4dcd3e8c92efa77cfa193b658940d9\\transformed\\appcompat-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,54,55,56,57,58,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,218,219,223,227,231,236,242,249,253,257,262,266,270,274,278,282,286,292,296,302,306,312,316,321,325,328,332,338,342,348,352,358,361,365,369,373,377,381,382,383,384,387,390,393,396,400,401,402,403,404,407,409,411,413,418,419,423,429,433,434,436,447,448,452,458,462,463,464,468,495,499,500,504,532,703,729,901,927,958,966,972,986,1008,1013,1018,1028,1037,1046,1050,1057,1065,1072,1073,1082,1085,1088,1092,1096,1100,1103,1104,1109,1114,1124,1129,1136,1142,1143,1146,1150,1155,1157,1159,1162,1165,1167,1171,1174,1181,1184,1187,1191,1193,1197,1199,1201,1203,1207,1215,1223,1235,1241,1250,1253,1264,1267,1268,1273,1274,1279,1348,1418,1419,1429,1438,1439,1441,1445,1448,1451,1454,1457,1460,1463,1466,1470,1473,1476,1479,1483,1486,1490,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1514,1516,1518,1519,1520,1521,1522,1523,1524,1525,1527,1528,1530,1531,1533,1535,1536,1538,1539,1540,1541,1542,1543,1545,1546,1547,1548,1549,1550,1552,1554,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1568,1570,1571,1572,1573,1574,1575,1577,1581,1585,1586,1587,1588,1589,1590,1594,1595,1596,1597,1599,1601,1603,1605,1607,1608,1609,1610,1612,1614,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1627,1630,1631,1632,1633,1635,1637,1638,1640,1641,1643,1645,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1658,1660,1661,1662,1663,1665,1666,1667,1668,1669,1671,1673,1675,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1692,1775,1778,1781,1784,1798,1809,1819,1849,1876,1885,1960,2357,2362,2390,2408,2444,2450,2456,2479,2620,2640,2646,2650,2656,2693,2705,2771,2795,2864,2883,2909", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,409,471,552,613,688,764,841,919,1004,1086,1162,1238,1315,1393,1499,1605,1684,1764,1821,1879,1953,2028,2093,2159,2219,2280,2352,2425,2492,2560,2619,2678,2737,2796,2855,2909,2963,3016,3070,3124,3178,3232,3306,3385,3458,3603,3675,3747,3820,3877,4008,4082,4156,4231,4303,4376,4446,4517,4577,4638,4707,4776,4846,4920,4996,5060,5137,5213,5290,5355,5424,5501,5576,5645,5713,5790,5856,5917,6014,6079,6148,6247,6318,6377,6435,6492,6551,6615,6686,6758,6830,6902,6974,7041,7109,7177,7236,7299,7363,7453,7544,7604,7670,7737,7803,7873,7937,7990,8057,8118,8185,8298,8356,8419,8484,8549,8624,8697,8769,8818,8879,8940,9001,9063,9127,9191,9255,9320,9383,9443,9504,9570,9629,9689,9751,9822,9882,9950,10036,10123,10213,10300,10388,10470,10553,10643,10734,10786,10844,10889,10955,11019,11076,11133,11187,11244,11292,11341,11392,11426,11473,11522,11568,11600,11664,11786,11843,11917,11987,12065,12119,12189,12274,12322,12368,12429,12492,12558,12622,12693,12756,12821,12885,12946,13007,13059,13132,13206,13275,13350,13424,13498,13639,13709,13762,13840,13930,14018,14114,14204,14786,14875,15122,15403,15655,15940,16333,16810,17032,17254,17530,17757,17987,18217,18447,18677,18904,19323,19549,19974,20204,20632,20851,21134,21342,21473,21700,22126,22351,22778,22999,23424,23544,23820,24121,24445,24736,25050,25187,25318,25423,25665,25832,26036,26244,26515,26627,26739,26844,26961,27175,27321,27461,27547,27895,27983,28229,28647,28896,28978,29076,29693,29793,30045,30469,30724,30818,30907,31144,33196,33438,33540,33793,35977,47101,48617,59840,61368,63125,63751,64171,65232,66497,66753,66989,67536,68030,68635,68833,69413,69977,70352,70470,71008,71165,71361,71634,71890,72060,72201,72265,72630,72997,73673,73937,74275,74628,74722,74908,75214,75476,75601,75728,75967,76178,76297,76490,76667,77122,77303,77425,77684,77797,77984,78086,78193,78322,78597,79105,79601,80478,80772,81342,81491,82223,82395,82479,82815,82907,83185,88594,94146,94208,94838,95452,95543,95656,95885,96045,96197,96368,96534,96703,96870,97033,97276,97446,97619,97790,98064,98263,98468,98798,98882,98978,99074,99172,99272,99374,99476,99578,99680,99782,99882,99978,100090,100219,100342,100473,100604,100702,100816,100910,101050,101184,101280,101392,101492,101608,101704,101816,101916,102056,102192,102356,102486,102644,102794,102935,103079,103214,103326,103476,103604,103732,103868,104000,104130,104260,104372,104512,104658,104802,104940,105006,105096,105172,105276,105366,105468,105576,105684,105784,105864,105956,106054,106164,106242,106348,106440,106544,106654,106776,106939,107096,107176,107276,107366,107476,107566,107807,107901,108007,108099,108199,108311,108425,108541,108657,108751,108865,108977,109079,109199,109321,109403,109507,109627,109753,109851,109945,110033,110145,110261,110383,110495,110670,110786,110872,110964,111076,111200,111267,111393,111461,111589,111733,111861,111930,112025,112140,112253,112352,112461,112572,112683,112784,112889,112989,113119,113210,113333,113427,113539,113625,113729,113825,113913,114031,114135,114239,114365,114453,114561,114661,114751,114861,114945,115047,115131,115185,115249,115355,115441,115551,115635,115755,120899,121017,121132,121264,121979,122671,123188,124787,126320,126708,131443,151705,151965,153475,154508,156521,156783,157139,157969,164751,165885,166179,166402,166729,168779,169427,173278,174480,178559,179774,181183", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,54,55,56,57,58,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,217,218,222,226,230,235,241,248,252,256,261,265,269,273,277,281,285,291,295,301,305,311,315,320,324,327,331,337,341,347,351,357,360,364,368,372,376,380,381,382,383,386,389,392,395,399,400,401,402,403,406,408,410,412,417,418,422,428,432,433,435,446,447,451,457,461,462,463,467,494,498,499,503,531,702,728,900,926,957,965,971,985,1007,1012,1017,1027,1036,1045,1049,1056,1064,1071,1072,1081,1084,1087,1091,1095,1099,1102,1103,1108,1113,1123,1128,1135,1141,1142,1145,1149,1154,1156,1158,1161,1164,1166,1170,1173,1180,1183,1186,1190,1192,1196,1198,1200,1202,1206,1214,1222,1234,1240,1249,1252,1263,1266,1267,1272,1273,1278,1347,1417,1418,1428,1437,1438,1440,1444,1447,1450,1453,1456,1459,1462,1465,1469,1472,1475,1478,1482,1485,1489,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1515,1517,1518,1519,1520,1521,1522,1523,1524,1526,1527,1529,1530,1532,1534,1535,1537,1538,1539,1540,1541,1542,1544,1545,1546,1547,1548,1549,1551,1553,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1569,1570,1571,1572,1573,1574,1576,1580,1584,1585,1586,1587,1588,1589,1593,1594,1595,1596,1598,1600,1602,1604,1606,1607,1608,1609,1611,1613,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1629,1630,1631,1632,1634,1636,1637,1639,1640,1642,1644,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1659,1660,1661,1662,1664,1665,1666,1667,1668,1670,1672,1674,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1774,1777,1780,1783,1797,1808,1818,1848,1875,1884,1959,2356,2361,2389,2407,2443,2449,2455,2478,2619,2639,2645,2649,2655,2692,2704,2770,2794,2863,2882,2908,2917", "endColumns": "54,44,48,40,54,58,61,80,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,71,71,72,56,57,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,404,466,547,608,683,759,836,914,999,1081,1157,1233,1310,1388,1494,1600,1679,1759,1816,1874,1948,2023,2088,2154,2214,2275,2347,2420,2487,2555,2614,2673,2732,2791,2850,2904,2958,3011,3065,3119,3173,3227,3301,3380,3453,3527,3670,3742,3815,3872,3930,4077,4151,4226,4298,4371,4441,4512,4572,4633,4702,4771,4841,4915,4991,5055,5132,5208,5285,5350,5419,5496,5571,5640,5708,5785,5851,5912,6009,6074,6143,6242,6313,6372,6430,6487,6546,6610,6681,6753,6825,6897,6969,7036,7104,7172,7231,7294,7358,7448,7539,7599,7665,7732,7798,7868,7932,7985,8052,8113,8180,8293,8351,8414,8479,8544,8619,8692,8764,8813,8874,8935,8996,9058,9122,9186,9250,9315,9378,9438,9499,9565,9624,9684,9746,9817,9877,9945,10031,10118,10208,10295,10383,10465,10548,10638,10729,10781,10839,10884,10950,11014,11071,11128,11182,11239,11287,11336,11387,11421,11468,11517,11563,11595,11659,11721,11838,11912,11982,12060,12114,12184,12269,12317,12363,12424,12487,12553,12617,12688,12751,12816,12880,12941,13002,13054,13127,13201,13270,13345,13419,13493,13634,13704,13757,13835,13925,14013,14109,14199,14781,14870,15117,15398,15650,15935,16328,16805,17027,17249,17525,17752,17982,18212,18442,18672,18899,19318,19544,19969,20199,20627,20846,21129,21337,21468,21695,22121,22346,22773,22994,23419,23539,23815,24116,24440,24731,25045,25182,25313,25418,25660,25827,26031,26239,26510,26622,26734,26839,26956,27170,27316,27456,27542,27890,27978,28224,28642,28891,28973,29071,29688,29788,30040,30464,30719,30813,30902,31139,33191,33433,33535,33788,35972,47096,48612,59835,61363,63120,63746,64166,65227,66492,66748,66984,67531,68025,68630,68828,69408,69972,70347,70465,71003,71160,71356,71629,71885,72055,72196,72260,72625,72992,73668,73932,74270,74623,74717,74903,75209,75471,75596,75723,75962,76173,76292,76485,76662,77117,77298,77420,77679,77792,77979,78081,78188,78317,78592,79100,79596,80473,80767,81337,81486,82218,82390,82474,82810,82902,83180,88589,94141,94203,94833,95447,95538,95651,95880,96040,96192,96363,96529,96698,96865,97028,97271,97441,97614,97785,98059,98258,98463,98793,98877,98973,99069,99167,99267,99369,99471,99573,99675,99777,99877,99973,100085,100214,100337,100468,100599,100697,100811,100905,101045,101179,101275,101387,101487,101603,101699,101811,101911,102051,102187,102351,102481,102639,102789,102930,103074,103209,103321,103471,103599,103727,103863,103995,104125,104255,104367,104507,104653,104797,104935,105001,105091,105167,105271,105361,105463,105571,105679,105779,105859,105951,106049,106159,106237,106343,106435,106539,106649,106771,106934,107091,107171,107271,107361,107471,107561,107802,107896,108002,108094,108194,108306,108420,108536,108652,108746,108860,108972,109074,109194,109316,109398,109502,109622,109748,109846,109940,110028,110140,110256,110378,110490,110665,110781,110867,110959,111071,111195,111262,111388,111456,111584,111728,111856,111925,112020,112135,112248,112347,112456,112567,112678,112779,112884,112984,113114,113205,113328,113422,113534,113620,113724,113820,113908,114026,114130,114234,114360,114448,114556,114656,114746,114856,114940,115042,115126,115180,115244,115350,115436,115546,115630,115750,120894,121012,121127,121259,121974,122666,123183,124782,126315,126703,131438,151700,151960,153470,154503,156516,156778,157134,157964,164746,165880,166174,166397,166724,168774,169422,173273,174475,178554,179769,181178,181652"}, "to": {"startLines": "4,27,28,59,60,61,62,64,65,66,67,68,69,72,73,74,75,76,77,78,79,80,81,86,87,90,91,92,93,94,95,96,97,98,99,101,102,103,104,105,106,107,108,109,110,111,112,117,118,119,120,122,123,124,125,126,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,224,225,229,230,231,232,233,234,235,261,262,263,264,265,266,267,268,304,305,306,307,312,321,322,327,349,355,356,358,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,416,421,422,423,424,425,426,434,435,439,443,447,452,458,465,469,473,478,482,486,490,494,498,502,508,512,518,522,528,532,537,541,544,548,554,558,564,568,574,577,581,585,589,593,597,598,599,600,603,606,609,612,616,617,618,619,620,623,625,627,629,634,635,639,645,649,650,652,663,664,668,674,678,679,680,684,711,715,716,720,748,918,944,1115,1141,1172,1180,1186,1200,1222,1227,1232,1242,1251,1260,1264,1271,1279,1286,1287,1296,1299,1302,1306,1310,1314,1317,1318,1323,1328,1338,1343,1350,1356,1357,1360,1364,1369,1371,1373,1376,1379,1381,1385,1388,1395,1398,1401,1405,1407,1411,1413,1415,1417,1421,1429,1437,1449,1455,1464,1467,1478,1481,1482,1487,1488,1517,1586,1656,1657,1667,1676,1828,1830,1834,1837,1840,1843,1846,1849,1852,1855,1859,1862,1865,1868,1872,1875,1879,1883,1884,1885,1886,1887,1888,1889,1890,1891,1892,1893,1894,1895,1896,1897,1898,1899,1900,1901,1902,1903,1905,1907,1908,1909,1910,1911,1912,1913,1914,1916,1917,1919,1920,1922,1924,1925,1927,1928,1929,1930,1931,1932,1934,1935,1936,1937,1938,1955,1957,1959,1961,1962,1963,1964,1965,1966,1967,1968,1969,1970,1971,1972,1973,1975,1976,1977,1978,1979,1980,1982,1986,1990,1991,1992,1993,1994,1995,1999,2000,2001,2002,2004,2006,2008,2010,2012,2013,2014,2015,2017,2019,2021,2022,2023,2024,2025,2026,2027,2028,2029,2030,2031,2032,2035,2036,2037,2038,2040,2042,2043,2045,2046,2048,2050,2052,2053,2054,2055,2056,2057,2058,2059,2060,2061,2062,2063,2065,2066,2067,2068,2070,2071,2072,2073,2074,2076,2078,2080,2082,2083,2084,2085,2086,2087,2088,2089,2090,2091,2092,2093,2094,2095,2096,2102,2177,2180,2183,2186,2200,2217,2259,2288,2315,2324,2386,2750,2781,2919,3043,3067,3073,3089,3110,3234,3262,3268,3412,3438,3486,3557,3657,3677,3732,3744,3770", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "225,891,936,1983,2024,2079,2138,2273,2354,2415,2490,2566,2643,2881,2966,3048,3124,3200,3277,3355,3461,3567,3646,3975,4032,4221,4295,4370,4435,4501,4561,4622,4694,4767,4834,4959,5018,5077,5136,5195,5254,5308,5362,5415,5469,5523,5577,5921,5995,6074,6147,6292,6364,6436,6509,6566,6697,6771,6845,6920,6992,7065,7135,7206,7266,7327,7396,7465,7535,7609,7685,7749,7826,7902,7979,8044,8113,8190,8265,8334,8402,8479,8545,8606,8703,8768,8837,8936,9007,9066,9124,9181,9240,9304,9375,9447,9519,9591,9663,9730,9798,9866,9925,9988,10052,10142,10233,10293,10359,10426,10492,10562,10626,10679,10746,10807,10874,10987,11045,11108,11173,11238,11313,11386,11458,11507,11568,11629,11690,11752,11816,11880,11944,12009,12072,12132,12193,12259,12318,12378,12440,12511,12571,13270,13356,13606,13696,13783,13871,13953,14036,14126,15851,15903,15961,16006,16072,16136,16193,16250,18427,18484,18532,18581,18836,19268,19315,19573,20744,21047,21111,21233,21486,21560,21630,21708,21762,21832,21917,21965,22011,22072,22135,22201,22265,22336,22399,22464,22528,22589,22650,22702,22775,22849,22918,22993,23067,23141,23282,25154,25515,25593,25683,25771,25867,25957,26539,26628,26875,27156,27408,27693,28086,28563,28785,29007,29283,29510,29740,29970,30200,30430,30657,31076,31302,31727,31957,32385,32604,32887,33095,33226,33453,33879,34104,34531,34752,35177,35297,35573,35874,36198,36489,36803,36940,37071,37176,37418,37585,37789,37997,38268,38380,38492,38597,38714,38928,39074,39214,39300,39648,39736,39982,40400,40649,40731,40829,41421,41521,41773,42197,42452,42546,42635,42872,44896,45138,45240,45493,47649,58181,59697,70328,71856,73613,74239,74659,75720,76985,77241,77477,78024,78518,79123,79321,79901,80465,80840,80958,81496,81653,81849,82122,82378,82548,82689,82753,83118,83485,84161,84425,84763,85116,85210,85396,85702,85964,86089,86216,86455,86666,86785,86978,87155,87610,87791,87913,88172,88285,88472,88574,88681,88810,89085,89593,90089,90966,91260,91830,91979,92711,92883,92967,93303,93395,95461,100707,106096,106158,106736,107320,115267,115380,115609,115769,115921,116092,116258,116427,116594,116757,117000,117170,117343,117514,117788,117987,118192,118522,118606,118702,118798,118896,118996,119098,119200,119302,119404,119506,119606,119702,119814,119943,120066,120197,120328,120426,120540,120634,120774,120908,121004,121116,121216,121332,121428,121540,121640,121780,121916,122080,122210,122368,122518,122659,122803,122938,123050,123200,123328,123456,123592,123724,123854,123984,124096,125376,125522,125666,125804,125870,125960,126036,126140,126230,126332,126440,126548,126648,126728,126820,126918,127028,127106,127212,127304,127408,127518,127640,127803,127960,128040,128140,128230,128340,128430,128671,128765,128871,128963,129063,129175,129289,129405,129521,129615,129729,129841,129943,130063,130185,130267,130371,130491,130617,130715,130809,130897,131009,131125,131247,131359,131534,131650,131736,131828,131940,132064,132131,132257,132325,132453,132597,132725,132794,132889,133004,133117,133216,133325,133436,133547,133648,133753,133853,133983,134074,134197,134291,134403,134489,134593,134689,134777,134895,134999,135103,135229,135317,135425,135525,135615,135725,135809,135911,135995,136049,136113,136219,136305,136415,136499,136903,139519,139637,139752,139832,140193,140779,142183,143527,144888,145276,148051,158140,159180,165993,170294,171045,171307,171839,172218,176496,177350,177579,182187,183197,184732,187132,191256,192000,194131,194471,195782", "endLines": "4,27,28,59,60,61,62,64,65,66,67,68,69,72,73,74,75,76,77,78,79,80,81,86,87,90,91,92,93,94,95,96,97,98,99,101,102,103,104,105,106,107,108,109,110,111,112,117,118,119,120,122,123,124,125,126,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,224,225,229,230,231,232,233,234,235,261,262,263,264,265,266,267,268,304,305,306,307,312,321,322,327,349,355,356,358,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,416,421,422,423,424,425,433,434,438,442,446,451,457,464,468,472,477,481,485,489,493,497,501,507,511,517,521,527,531,536,540,543,547,553,557,563,567,573,576,580,584,588,592,596,597,598,599,602,605,608,611,615,616,617,618,619,622,624,626,628,633,634,638,644,648,649,651,662,663,667,673,677,678,679,683,710,714,715,719,747,917,943,1114,1140,1171,1179,1185,1199,1221,1226,1231,1241,1250,1259,1263,1270,1278,1285,1286,1295,1298,1301,1305,1309,1313,1316,1317,1322,1327,1337,1342,1349,1355,1356,1359,1363,1368,1370,1372,1375,1378,1380,1384,1387,1394,1397,1400,1404,1406,1410,1412,1414,1416,1420,1428,1436,1448,1454,1463,1466,1477,1480,1481,1486,1487,1492,1585,1655,1656,1666,1675,1676,1829,1833,1836,1839,1842,1845,1848,1851,1854,1858,1861,1864,1867,1871,1874,1878,1882,1883,1884,1885,1886,1887,1888,1889,1890,1891,1892,1893,1894,1895,1896,1897,1898,1899,1900,1901,1902,1904,1906,1907,1908,1909,1910,1911,1912,1913,1915,1916,1918,1919,1921,1923,1924,1926,1927,1928,1929,1930,1931,1933,1934,1935,1936,1937,1938,1956,1958,1960,1961,1962,1963,1964,1965,1966,1967,1968,1969,1970,1971,1972,1974,1975,1976,1977,1978,1979,1981,1985,1989,1990,1991,1992,1993,1994,1998,1999,2000,2001,2003,2005,2007,2009,2011,2012,2013,2014,2016,2018,2020,2021,2022,2023,2024,2025,2026,2027,2028,2029,2030,2031,2034,2035,2036,2037,2039,2041,2042,2044,2045,2047,2049,2051,2052,2053,2054,2055,2056,2057,2058,2059,2060,2061,2062,2064,2065,2066,2067,2069,2070,2071,2072,2073,2075,2077,2079,2081,2082,2083,2084,2085,2086,2087,2088,2089,2090,2091,2092,2093,2094,2095,2096,2176,2179,2182,2185,2199,2205,2226,2287,2314,2323,2385,2744,2753,2808,2936,3066,3072,3078,3109,3233,3253,3267,3271,3417,3472,3497,3622,3676,3731,3743,3769,3776", "endColumns": "54,44,48,40,54,58,61,80,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,71,71,72,56,57,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "275,931,980,2019,2074,2133,2195,2349,2410,2485,2561,2638,2716,2961,3043,3119,3195,3272,3350,3456,3562,3641,3721,4027,4085,4290,4365,4430,4496,4556,4617,4689,4762,4829,4897,5013,5072,5131,5190,5249,5303,5357,5410,5464,5518,5572,5626,5990,6069,6142,6216,6359,6431,6504,6561,6619,6766,6840,6915,6987,7060,7130,7201,7261,7322,7391,7460,7530,7604,7680,7744,7821,7897,7974,8039,8108,8185,8260,8329,8397,8474,8540,8601,8698,8763,8832,8931,9002,9061,9119,9176,9235,9299,9370,9442,9514,9586,9658,9725,9793,9861,9920,9983,10047,10137,10228,10288,10354,10421,10487,10557,10621,10674,10741,10802,10869,10982,11040,11103,11168,11233,11308,11381,11453,11502,11563,11624,11685,11747,11811,11875,11939,12004,12067,12127,12188,12254,12313,12373,12435,12506,12566,12634,13351,13438,13691,13778,13866,13948,14031,14121,14212,15898,15956,16001,16067,16131,16188,16245,16299,18479,18527,18576,18627,18865,19310,19359,19614,20771,21106,21168,21285,21555,21625,21703,21757,21827,21912,21960,22006,22067,22130,22196,22260,22331,22394,22459,22523,22584,22645,22697,22770,22844,22913,22988,23062,23136,23277,23347,25202,25588,25678,25766,25862,25952,26534,26623,26870,27151,27403,27688,28081,28558,28780,29002,29278,29505,29735,29965,30195,30425,30652,31071,31297,31722,31952,32380,32599,32882,33090,33221,33448,33874,34099,34526,34747,35172,35292,35568,35869,36193,36484,36798,36935,37066,37171,37413,37580,37784,37992,38263,38375,38487,38592,38709,38923,39069,39209,39295,39643,39731,39977,40395,40644,40726,40824,41416,41516,41768,42192,42447,42541,42630,42867,44891,45133,45235,45488,47644,58176,59692,70323,71851,73608,74234,74654,75715,76980,77236,77472,78019,78513,79118,79316,79896,80460,80835,80953,81491,81648,81844,82117,82373,82543,82684,82748,83113,83480,84156,84420,84758,85111,85205,85391,85697,85959,86084,86211,86450,86661,86780,86973,87150,87605,87786,87908,88167,88280,88467,88569,88676,88805,89080,89588,90084,90961,91255,91825,91974,92706,92878,92962,93298,93390,93668,100702,106091,106153,106731,107315,107406,115375,115604,115764,115916,116087,116253,116422,116589,116752,116995,117165,117338,117509,117783,117982,118187,118517,118601,118697,118793,118891,118991,119093,119195,119297,119399,119501,119601,119697,119809,119938,120061,120192,120323,120421,120535,120629,120769,120903,120999,121111,121211,121327,121423,121535,121635,121775,121911,122075,122205,122363,122513,122654,122798,122933,123045,123195,123323,123451,123587,123719,123849,123979,124091,124231,125517,125661,125799,125865,125955,126031,126135,126225,126327,126435,126543,126643,126723,126815,126913,127023,127101,127207,127299,127403,127513,127635,127798,127955,128035,128135,128225,128335,128425,128666,128760,128866,128958,129058,129170,129284,129400,129516,129610,129724,129836,129938,130058,130180,130262,130366,130486,130612,130710,130804,130892,131004,131120,131242,131354,131529,131645,131731,131823,131935,132059,132126,132252,132320,132448,132592,132720,132789,132884,132999,133112,133211,133320,133431,133542,133643,133748,133848,133978,134069,134192,134286,134398,134484,134588,134684,134772,134890,134994,135098,135224,135312,135420,135520,135610,135720,135804,135906,135990,136044,136108,136214,136300,136410,136494,136614,139514,139632,139747,139827,140188,140421,141291,143522,144883,145271,148046,157950,158270,160532,166560,171040,171302,171502,172213,176491,177097,177574,177725,182397,184275,185039,190153,191995,194126,194466,195777,195980"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8cf478dec41eed746328fa8046755ba2\\transformed\\core-1.13.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "29,70,71,88,89,113,114,217,218,219,220,221,222,223,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,314,315,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,361,390,391,392,393,394,395,396,417,1939,1940,1945,1948,1953,2097,2098,2754,2771,2941,2974,3004,3037", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "985,2721,2793,4090,4155,5631,5700,12782,12852,12920,12992,13062,13123,13197,14440,14501,14562,14624,14688,14750,14811,14879,14979,15039,15105,15178,15247,15304,15356,16304,16376,16452,16517,16576,16635,16695,16755,16815,16875,16935,16995,17055,17115,17175,17235,17294,17354,17414,17474,17534,17594,17654,17714,17774,17834,17894,17953,18013,18073,18132,18191,18250,18309,18368,18936,18971,19619,19674,19737,19792,19850,19908,19969,20032,20089,20140,20190,20251,20308,20374,20408,20443,21416,23435,23502,23574,23643,23712,23786,23858,25207,124236,124353,124620,124913,125180,136619,136691,158275,158879,166714,168445,169445,170127", "endLines": "29,70,71,88,89,113,114,217,218,219,220,221,222,223,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,314,315,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,361,390,391,392,393,394,395,396,417,1939,1943,1945,1951,1953,2097,2098,2759,2780,2973,2994,3036,3042", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "1040,2788,2876,4150,4216,5695,5758,12847,12915,12987,13057,13118,13192,13265,14496,14557,14619,14683,14745,14806,14874,14974,15034,15100,15173,15242,15299,15351,15413,16371,16447,16512,16571,16630,16690,16750,16810,16870,16930,16990,17050,17110,17170,17230,17289,17349,17409,17469,17529,17589,17649,17709,17769,17829,17889,17948,18008,18068,18127,18186,18245,18304,18363,18422,18966,19001,19669,19732,19787,19845,19903,19964,20027,20084,20135,20185,20246,20303,20369,20403,20438,20473,21481,23497,23569,23638,23707,23781,23853,23941,25273,124348,124549,124725,125109,125304,136686,136753,158473,159175,168440,169121,170122,170289"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2c7a28485314ee0e74d343b62dcecbb6\\transformed\\media-1.7.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,144,215,288,350,410,476,598,659,725", "endColumns": "88,70,72,61,59,65,121,60,65,66", "endOffsets": "139,210,283,345,405,471,593,654,720,787"}, "to": {"startLines": "115,121,127,316,357,1944,1946,1947,1952,1954", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "5763,6221,6624,19006,21173,124554,124730,124852,125114,125309", "endColumns": "88,70,72,61,59,65,121,60,65,66", "endOffsets": "5847,6287,6692,19063,21228,124615,124847,124908,125175,125371"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ab1dc0cd23ebe3c890248eaabfbb4ea4\\transformed\\jetified-media3-exoplayer-1.4.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,187,252,316,393,458,548,632", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "120,182,247,311,388,453,543,627,696"}, "to": {"startLines": "399,400,401,402,403,404,405,406,407", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "24052,24122,24184,24249,24313,24390,24455,24545,24629", "endColumns": "69,61,64,63,76,64,89,83,68", "endOffsets": "24117,24179,24244,24308,24385,24450,24540,24624,24693"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c092edbccc16347970ed4f22e8da111a\\transformed\\lifecycle-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "350", "startColumns": "4", "startOffsets": "20776", "endColumns": "42", "endOffsets": "20814"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e0d9d3675465ff69d847e2f781f20c61\\transformed\\jetified-startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "389", "startColumns": "4", "startOffsets": "23352", "endColumns": "82", "endOffsets": "23430"}}, {"source": "E:\\Qorane\\qorane\\android\\app\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "3,14", "startColumns": "4,4", "startOffsets": "176,832", "endLines": "7,16", "endColumns": "12,12", "endOffsets": "483,998"}, "to": {"startLines": "1510,1514", "startColumns": "4,4", "startOffsets": "95111,95292", "endLines": "1513,1516", "endColumns": "12,12", "endOffsets": "95287,95456"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5093ab42d2307deb2d7ac0b7f5718c38\\transformed\\jetified-window-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,3,9,17,25,37,43,49,50,51,52,53,54,55,61,66,74,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,287,506,725,1039,1227,1414,1467,1527,1579,1624,1663,1723,1918,2076,2358,2972", "endLines": "2,8,16,24,36,42,48,49,50,51,52,53,54,60,65,73,88,104", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "109,282,501,720,1034,1222,1409,1462,1522,1574,1619,1658,1718,1913,2071,2353,2967,3621"}, "to": {"startLines": "2,5,11,19,30,42,48,54,55,56,57,58,308,2206,2212,3498,3506,3521", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,280,453,672,1045,1359,1547,1734,1787,1847,1899,1944,18632,140426,140621,185044,185326,185940", "endLines": "2,10,18,26,41,47,53,54,55,56,57,58,308,2211,2216,3505,3520,3536", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "159,448,667,886,1354,1542,1729,1782,1842,1894,1939,1978,18687,140616,140774,185321,185935,186589"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\30b0283ed69cc5a036226586199afb07\\transformed\\jetified-appcompat-resources-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,29,36,47,74", "startColumns": "4,4,4,4,4", "startOffsets": "55,1702,2087,2684,4317", "endLines": "28,35,46,73,78", "endColumns": "24,24,24,24,24", "endOffsets": "1697,2082,2679,4312,4582"}, "to": {"startLines": "2227,2243,2249,3537,3553", "startColumns": "4,4,4,4,4", "startOffsets": "141296,141721,141899,186594,187005", "endLines": "2242,2248,2258,3552,3556", "endColumns": "24,24,24,24,24", "endOffsets": "141716,141894,142178,187000,187127"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\45af1ebc35cbf9d2d2886a132166b73a\\transformed\\transition-1.4.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,95,142,185,240,287,341,393,442,503", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "90,137,180,235,282,336,388,437,498,548"}, "to": {"startLines": "310,311,317,324,325,344,345,346,347,348", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "18749,18789,19068,19406,19461,20478,20532,20584,20633,20694", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "18784,18831,19106,19456,19503,20527,20579,20628,20689,20739"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\75e1bcd7a8b61b1e132d50e7766bfd37\\transformed\\recyclerview-1.0.0\\res\\values\\values.xml", "from": {"startLines": "30,31,32,33,34,35,36,2", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "1535,1594,1642,1698,1773,1849,1921,55", "endLines": "30,31,32,33,34,35,36,29", "endColumns": "58,47,55,74,75,71,65,24", "endOffsets": "1589,1637,1693,1768,1844,1916,1982,1530"}, "to": {"startLines": "226,227,228,236,237,238,313,3418", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "13443,13502,13550,14217,14292,14368,18870,182402", "endLines": "226,227,228,236,237,238,313,3437", "endColumns": "58,47,55,74,75,71,65,24", "endOffsets": "13497,13545,13601,14287,14363,14435,18931,183192"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\308a2e77faa557d0bd706972416bde6a\\transformed\\browser-1.8.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,179,242,304,375,447,515,582,661", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "108,174,237,299,370,442,510,577,656,725"}, "to": {"startLines": "82,83,84,85,215,216,398,409,410,411", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "3726,3784,3850,3913,12639,12710,23984,24755,24822,24901", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "3779,3845,3908,3970,12705,12777,24047,24817,24896,24965"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\58a0920e123e93dd6aa702d27ab7530e\\transformed\\coordinatorlayout-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,102,3,13", "startColumns": "4,4,4,4", "startOffsets": "55,5935,116,724", "endLines": "2,104,12,101", "endColumns": "60,12,24,24", "endOffsets": "111,6075,719,5930"}, "to": {"startLines": "3,2099,2809,2815", "startColumns": "4,4,4,4", "startOffsets": "164,136758,160537,160748", "endLines": "3,2101,2814,2898", "endColumns": "60,12,24,24", "endOffsets": "220,136898,160743,165259"}}, {"source": "E:\\Qorane\\qorane\\android\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "56", "endOffsets": "107"}, "to": {"startLines": "100", "startColumns": "4", "startOffsets": "4902", "endColumns": "56", "endOffsets": "4954"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\13dd610fda78ecd8ad3daad9b8195d7e\\transformed\\jetified-activity-1.8.1\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "323,351", "startColumns": "4,4", "startOffsets": "19364,20819", "endColumns": "41,59", "endOffsets": "19401,20874"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1b91be3af319ede480d7185430690ee1\\transformed\\lifecycle-viewmodel-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "353", "startColumns": "4", "startOffsets": "20933", "endColumns": "49", "endOffsets": "20978"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0098a6e93522fecc805d8900172003dc\\transformed\\preference-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,22,23,24,25,42,45,51,57,60,66,70,73,80,86,89,95,100,105,112,114,120,126,134,139,146,151,157,161,168,172,178,184,187,192,193,194,199,215,238,243,257,268,348,358,368,386,392,439,461,485", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,178,247,311,366,434,501,566,623,680,728,776,837,900,963,1001,1058,1102,1242,1381,1431,1479,2917,3022,3378,3716,3862,4202,4414,4577,4984,5322,5445,5784,6023,6280,6651,6711,7049,7335,7784,8076,8464,8769,9113,9358,9688,9895,10163,10436,10580,10949,10996,11052,11308,12367,13788,14126,15012,15622,20168,20687,21229,22503,22763,25467,26989,28470", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,21,22,23,24,41,44,50,56,59,65,69,72,79,85,88,94,99,104,111,113,119,125,133,138,145,150,156,160,167,171,177,183,186,191,192,193,198,214,237,242,256,267,347,357,367,385,391,438,460,484,508", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "173,242,306,361,429,496,561,618,675,723,771,832,895,958,996,1053,1097,1237,1376,1426,1474,2912,3017,3373,3711,3857,4197,4409,4572,4979,5317,5440,5779,6018,6275,6646,6706,7044,7330,7779,8071,8459,8764,9108,9353,9683,9890,10158,10431,10575,10944,10991,11047,11303,12362,13783,14121,15007,15617,20163,20682,21224,22498,22758,25462,26984,28465,29984"}, "to": {"startLines": "63,116,254,255,256,257,258,259,260,318,319,320,359,360,397,408,412,413,418,419,420,1493,1677,1680,1686,1692,1695,1701,1705,1708,1715,1721,1724,1730,1735,1740,1747,1749,1755,1761,1769,1774,1781,1786,1792,1796,1803,1807,1813,1819,1822,1826,1827,2745,2760,2899,2937,3079,3254,3272,3336,3346,3356,3363,3369,3473,3623,3640", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2200,5852,15418,15482,15537,15605,15672,15737,15794,19111,19159,19207,21290,21353,23946,24698,24970,25014,25278,25417,25467,93673,107411,107516,107761,108099,108245,108585,108797,108960,109367,109705,109828,110167,110406,110663,111034,111094,111432,111718,112167,112459,112847,113152,113496,113741,114071,114278,114546,114819,114963,115164,115211,157955,158478,165264,166565,171507,177102,177730,179655,179937,180242,180504,180764,184280,190158,190688", "endLines": "63,116,254,255,256,257,258,259,260,318,319,320,359,360,397,408,412,415,418,419,420,1509,1679,1685,1691,1694,1700,1704,1707,1714,1720,1723,1729,1734,1739,1746,1748,1754,1760,1768,1773,1780,1785,1791,1795,1802,1806,1812,1818,1821,1825,1826,1827,2749,2770,2918,2940,3088,3261,3335,3345,3355,3362,3368,3411,3485,3639,3656", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "2268,5916,15477,15532,15600,15667,15732,15789,15846,19154,19202,19263,21348,21411,23979,24750,25009,25149,25412,25462,25510,95106,107511,107756,108094,108240,108580,108792,108955,109362,109700,109823,110162,110401,110658,111029,111089,111427,111713,112162,112454,112842,113147,113491,113736,114066,114273,114541,114814,114958,115159,115206,115262,158135,158874,165988,166709,171834,177345,179650,179932,180237,180499,180759,182182,184727,190683,191251"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\55520e4df2220e27f13f0bbb7467d11a\\transformed\\fragment-1.7.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "309,326,354,2995,3000", "startColumns": "4,4,4,4,4", "startOffsets": "18692,19508,20983,169126,169296", "endLines": "309,326,354,2999,3003", "endColumns": "56,64,63,24,24", "endOffsets": "18744,19568,21042,169291,169440"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\b3d51a44ab6b56289d4858158a1ad6dd\\transformed\\jetified-savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "352", "startColumns": "4", "startOffsets": "20879", "endColumns": "53", "endOffsets": "20928"}}]}]}
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/favorites_provider.dart';
import '../providers/background_audio_provider.dart'; // استخدام مزود الصوت المحسن

class SurahPlayerScreen extends StatefulWidget {
  final String audioPath;
  final String surahName;

  const SurahPlayerScreen({
    super.key,
    required this.audioPath,
    required this.surahName,
  });

  @override
  State<SurahPlayerScreen> createState() => _SurahPlayerScreenState();
}

class _SurahPlayerScreenState extends State<SurahPlayerScreen> with WidgetsBindingObserver {
  @override
  void initState() {
    super.initState();
    debugPrint('===== INITIALIZING SURAH PLAYER SCREEN =====');
    debugPrint('Surah: ${widget.surahName}, Path: ${widget.audioPath}');

    WidgetsBinding.instance.addObserver(this);

    // تأخير قصير لضمان تهيئة المزود
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final audioProvider = Provider.of<BackgroundAudioProvider>(context, listen: false);
      debugPrint('Initializing audio player with path: ${widget.audioPath}');

      // تشغيل السورة فقط إذا لم تكن تعمل بالفعل أو إذا كانت سورة مختلفة
      if (audioProvider.currentAudioPath != widget.audioPath) {
        audioProvider.playSurah(widget.audioPath, widget.surahName);
      }
    });
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    // لا نحتاج لإيقاف الصوت عند تغيير حالة التطبيق
    // لأننا نريد التشغيل في الخلفية
    // if (state == AppLifecycleState.paused) {
    //   final audioProvider = Provider.of<BackgroundAudioProvider>(context, listen: false);
    //   audioProvider.pause();
    // }
  }

  @override
  void dispose() {
    debugPrint('===== DISPOSING SURAH PLAYER SCREEN =====');
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        image: DecorationImage(
          image: AssetImage('assets/images/kalfea2.jpg'),
          fit: BoxFit.cover,
        ),
      ),
      child: Scaffold(
        backgroundColor: Colors.transparent,
        extendBodyBehindAppBar: true,
        appBar: AppBar(
          leading: IconButton(
            icon: const Icon(Icons.arrow_back, color: Colors.white),
            onPressed: () => Navigator.pop(context),
          ),
          title: null,
          backgroundColor: Colors.transparent,
          elevation: 0,
        ),
        body: Consumer<BackgroundAudioProvider>(
          builder: (context, audioProvider, child) {
            return Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    shape: BoxShape.circle,
                    image: const DecorationImage(
                      image: AssetImage('assets/images/kald algalel.png'),
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
                const SizedBox(height: 20),
                Text(
                  audioProvider.currentSurahName.isNotEmpty
                    ? audioProvider.currentSurahName
                    : widget.surahName,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 20),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    IconButton(
                      icon: const Icon(Icons.skip_previous, color: Colors.white, size: 40),
                      onPressed: () => audioProvider.playPrevious(),
                    ),
                    const SizedBox(width: 20),
                    CircleAvatar(
                      radius: 35,
                      backgroundColor: const Color(0xFF4C8C7D),
                      child: IconButton(
                        icon: Icon(
                          audioProvider.isPlaying ? Icons.pause : Icons.play_arrow,
                          color: Colors.amber,
                          size: 40,
                        ),
                        onPressed: () => audioProvider.togglePlayPause(),
                      ),
                    ),
                    const SizedBox(width: 20),
                    IconButton(
                      icon: const Icon(Icons.skip_next, color: Colors.white, size: 40),
                      onPressed: () => audioProvider.playNext(),
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: SliderTheme(
                    data: SliderTheme.of(context).copyWith(
                      activeTrackColor: Colors.amber,
                      inactiveTrackColor: Colors.amber.withAlpha(77),
                      thumbColor: const Color(0xFF4C8C7D),
                      trackHeight: 2.0,
                    ),
                    child: Slider(
                      value: audioProvider.position.inSeconds.toDouble(),
                      max: audioProvider.duration.inSeconds > 0
                          ? audioProvider.duration.inSeconds.toDouble()
                          : 1.0,
                      onChanged: (value) {
                        audioProvider.seek(Duration(seconds: value.toInt()));
                      },
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        audioProvider.formatTime(audioProvider.position),
                        style: const TextStyle(color: Colors.white),
                      ),
                      Text(
                        audioProvider.formatTime(audioProvider.duration),
                        style: const TextStyle(color: Colors.white),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 20),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // زر التكرار
                    IconButton(
                      icon: Icon(
                        Icons.repeat,
                        color: audioProvider.isLooping ? Colors.amber : Colors.white,
                        size: 30,
                      ),
                      onPressed: () => audioProvider.toggleLooping(),
                      tooltip: audioProvider.isLooping ? 'إلغاء التكرار' : 'تكرار السورة',
                    ),

                    const SizedBox(width: 20),

                    // زر المفضلة
                    IconButton(
                      icon: Consumer<FavoritesProvider>(
                        builder: (context, favoritesProvider, child) {
                          final isFavorite = favoritesProvider.isFavorite(widget.audioPath);
                          return Icon(
                            isFavorite ? Icons.favorite : Icons.favorite_border,
                            color: isFavorite ? Colors.red : Colors.amber,
                            size: 30,
                          );
                        },
                      ),
                      onPressed: () {
                        Provider.of<FavoritesProvider>(context, listen: false)
                            .toggleFavorite(widget.audioPath);
                      },
                      tooltip: 'المفضلة',
                    ),

                    const SizedBox(width: 20),

                    // زر التشغيل في الخلفية
                    IconButton(
                      icon: const Icon(
                        Icons.volume_up,
                        color: Colors.white,
                        size: 30,
                      ),
                      onPressed: () {
                        // إظهار رسالة تأكيد
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('التطبيق يعمل الآن في الخلفية. يمكنك التحكم في التشغيل من الإشعارات.'),
                            duration: Duration(seconds: 3),
                            backgroundColor: Color(0xFF4C8C7D),
                          ),
                        );
                        // العودة إلى الشاشة الرئيسية مع الاحتفاظ بالتشغيل
                        Navigator.pop(context);
                      },
                      tooltip: 'تشغيل في الخلفية',
                    ),
                  ],
                ),
              ],
            );
          },
        ),
      ),
    );
  }
}

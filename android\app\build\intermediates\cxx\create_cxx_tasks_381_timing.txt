# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 10ms
    [gap of 19ms]
    create-X86-model 82ms
    create-X86_64-model 17ms
    [gap of 128ms]
    create-module-model 86ms
    [gap of 31ms]
    create-ARMEABI_V7A-model 17ms
    create-X86-model 194ms
    create-X86_64-model 15ms
    create-module-model 18ms
    create-variant-model 31ms
    create-ARM64_V8A-model 115ms
    [gap of 11ms]
  create-initial-cxx-model completed in 788ms
create_cxx_tasks completed in 790ms


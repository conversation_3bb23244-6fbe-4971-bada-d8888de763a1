{"logs": [{"outputFile": "com.example.qorane.app-mergeDebugResources-53:/values-hi/values-hi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\308a2e77faa557d0bd706972416bde6a\\transformed\\browser-1.8.0\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,161,263,375", "endColumns": "105,101,111,102", "endOffsets": "156,258,370,473"}, "to": {"startLines": "37,48,49,50", "startColumns": "4,4,4,4", "startOffsets": "3613,4578,4680,4792", "endColumns": "105,101,111,102", "endOffsets": "3714,4675,4787,4890"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8cf478dec41eed746328fa8046755ba2\\transformed\\core-1.13.1\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,361,462,575,681,808", "endColumns": "97,102,104,100,112,105,126,100", "endOffsets": "148,251,356,457,570,676,803,904"}, "to": {"startLines": "29,30,31,32,33,34,35,54", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2787,2885,2988,3093,3194,3307,3413,5196", "endColumns": "97,102,104,100,112,105,126,100", "endOffsets": "2880,2983,3088,3189,3302,3408,3535,5292"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ab1dc0cd23ebe3c890248eaabfbb4ea4\\transformed\\jetified-media3-exoplayer-1.4.1\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,189,260,328,424,492,615,736", "endColumns": "67,65,70,67,95,67,122,120,86", "endOffsets": "118,184,255,323,419,487,610,731,818"}, "to": {"startLines": "38,39,40,41,42,43,44,45,46", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3719,3787,3853,3924,3992,4088,4156,4279,4400", "endColumns": "67,65,70,67,95,67,122,120,86", "endOffsets": "3782,3848,3919,3987,4083,4151,4274,4395,4482"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3a4dcd3e8c92efa77cfa193b658940d9\\transformed\\appcompat-1.1.0\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,211,309,419,505,607,728,806,884,975,1067,1162,1256,1357,1450,1545,1639,1730,1821,1901,2006,2108,2206,2316,2419,2528,2686,2787", "endColumns": "105,97,109,85,101,120,77,77,90,91,94,93,100,92,94,93,90,90,79,104,101,97,109,102,108,157,100,80", "endOffsets": "206,304,414,500,602,723,801,879,970,1062,1157,1251,1352,1445,1540,1634,1725,1816,1896,2001,2103,2201,2311,2414,2523,2681,2782,2863"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,53", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,211,309,419,505,607,728,806,884,975,1067,1162,1256,1357,1450,1545,1639,1730,1821,1901,2006,2108,2206,2316,2419,2528,2686,5115", "endColumns": "105,97,109,85,101,120,77,77,90,91,94,93,100,92,94,93,90,90,79,104,101,97,109,102,108,157,100,80", "endOffsets": "206,304,414,500,602,723,801,879,970,1062,1157,1251,1352,1445,1540,1634,1725,1816,1896,2001,2103,2201,2311,2414,2523,2681,2782,5191"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0098a6e93522fecc805d8900172003dc\\transformed\\preference-1.2.1\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,178,269,347,489,658,738", "endColumns": "72,90,77,141,168,79,77", "endOffsets": "173,264,342,484,653,733,811"}, "to": {"startLines": "36,47,51,52,55,56,57", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "3540,4487,4895,4973,5297,5466,5546", "endColumns": "72,90,77,141,168,79,77", "endOffsets": "3608,4573,4968,5110,5461,5541,5619"}}]}]}